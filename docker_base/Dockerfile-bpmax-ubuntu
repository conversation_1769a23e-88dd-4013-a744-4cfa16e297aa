
FROM ubuntu:20.04
COPY ./node-v16.20.2 /usr/local/
COPY ./share-nginx /usr/share/nginx
COPY ./sbin-nginx /usr/sbin/nginx
COPY ./etc-nginx /etc/nginx
WORKDIR /var/www/
ENV TZ=Asia/Shanghai
ARG DEBIAN_FRONTEND=noninteractive
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list \
    && sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list\
    && apt update && apt install -y zip gcc automake autoconf libtool make g++ build-essential libssl-dev libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev libpcre3 libpcre3-dev zlib1g zlib1g-dev locales git curl unzip rsync \
    && locale-gen zh_CN.UTF-8 && update-locale LANG=zh_CN.UTF-8 \
    && localedef -c -f UTF-8 -i zh_CN zh_CN.utf8 && export LANG="zh_CN.utf8" \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && mkdir -p /var/lib/nginx/tmp/client_body && mkdir /var/log/nginx && touch /var/log/nginx/error.log && touch /var/log/nginx/access.log \
    && npm set registry https://registry.npmmirror.com \
    && npm set disturl https://npmmirror.com/mirrors/node \
    && npm set node_inspector_cdnurl https://registry.npmmirror.com/mirrors/node-inspector \
    && npm set canvas_binary_host_mirror https://registry.npmmirror.com/-/binary/canvas \
    && npm i -g pm2 && npm i -g yarn && npm install -g foreman \
    && yarn config set canvas_binary_host_mirror https://registry.npmmirror.com/-/binary/canvas \
    && pm2 install pm2-logrotate && pm2 set pm2-logrotate:compress true && pm2 set pm2-logrotate:rotateInterval '0 3 * * *' && pm2 set pm2-logrotate:max_size 8000M && pm2 set pm2-logrotate:retain 2 && pm2 set pm2-logrotate:dateFormat "YYYY-MM-DD" \
    && npm cache clean -f \
    && apt clean && apt autoclean && apt autoremove
EXPOSE 8080
EXPOSE 80


