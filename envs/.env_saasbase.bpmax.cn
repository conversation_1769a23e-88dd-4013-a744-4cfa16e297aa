ALIACCESSKEYID=LTAI4GDNf4ut1x4r9ND8dcNc
ALIACCESSKEYSECRET=******************************
ALIUPLOADARN=
API_HOST=/api
APP_HOST=0.0.0.0
APP_LIMIT=2025-12-31T23:59:00
APP_MODE=saas
BPMAX_API_HOST=
BPMAX_ENGINE_SERVER=http://localhost:10001/api
BPMAX_SERVER=http://127.0.0.1:10003/bpmax
B_KAFKA_BROKERS=
B_KAFKA_CLIENT_ID=bpmax-huanpingtong
CONNECTION_LIMIT=220
CRYPTO_KEY=
DEFAULT_BAK_WORKSPACE=elearning
DEFAULT_WORKSPACE=xunjian
DOMAIN=saasbase.yitaiyitai.com
ENV_MYSQL_VERSION=8.0
ENV_TAG=saasbase
ES_NAMESPACE=
ES_SERVER=
ES_USER=
ES_PASSWORD=
GLOBAL_WATER_MARK=
KAFKA_BROKERS=
KAFKA_CLIENT_ID=bpmax-mysql-es
LOGGER=
MAX_OLD_SPACE_SIZE_AUTOMATION=1024
MAX_OLD_SPACE_SIZE_API=1024
MYSQL_DATABASE=
MYSQL_HOST=
MYSQL_PASSWORD=
MYSQL_PORT=
MYSQL_TYPE=mysql
MYSQL_USER=
NODEHOST=**********
NODE_TLS_REJECT_UNAUTHORIZED=0
OPEN_API_SECRET_KEY=saasbase_^asdasd%wpocm@noirfeoopmciuoi23
OPEN_APP_ID=
OPEN_APP_SECRET=
OPEN_CORPORATION_ID=
OSSBUCKET=bpmax-saas
OSSHOST=pw-garden.oss-cn-shanghai-internal.aliyuncs.com
OSSREEGION=oss-cn-shanghai
OSSUPLOADBUCKET=bpmax-saas
OSS_UPLOAD_PUBLIC=0
PLUGIN_OSS_BASE=/saasbase_yitaiyitai_com
PROTOCOL=https
PUBLICPATH_MOBILE=/saasbase_yitaiyitai_com/mobile
PUBLICPATH_PC=/saasbase_yitaiyitai_com/pc
REDIS_DEFAULT_EXPIRE=600
REDIS_HOST=
REDIS_KEY_PREFIX=saasbase_yitaiyitai_com:
REDIS_PASSWORD=0Cb+PGUsnB3K
REDIS_PORT=6379
RUNTIME_ENV=test
SECRET_KEY=saasbase_yitaiyitai_comjuyewrewrmnc^8sdjk
SERVER_TYPE=automation,cluster,api
SESSION_REDIS_KEY_PREFIX=
SHOWI18N=
SIGNNAME=零蝉智能
SMSCODE=ff32
SYNC_GROUP_USER_TOPIC_PREFIX=
WORKER_COUNT_API=4
WORKER_COUNT_AUTOMATION=2
WXAPPID=
WXAPPSECRET=
containerName=saasbase_yitaiyitai_com
mode=production
port=10108
FEISHUJUMPER_DOMAIN=https://feishujumper.bpmax.cn/
