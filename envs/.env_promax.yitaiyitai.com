# ProMax 流程挖掘平台生产环境配置
# 统一的环境变量配置文件

# ==========================================
# 服务器信息
# ==========================================
SERVER_HOST=*************
SERVER_USER=bpmax4090
CONTAINER_NAME=promax-platform
IMAGE_NAME=promax-platform
IMAGE_TAG=latest
REMOTE_DIR=/home/<USER>/promax-deployment

# ==========================================
# 数据库配置 (MySQL)
# ==========================================
DB_HOST=**************
DB_PORT=33068
DB_USERNAME=root
DB_PASSWORD=J#fe9Yu{
DB_DATABASE=promax_db
DB_TYPE=mysql

# Python服务使用的MySQL变量名
MYSQL_HOST=**************
MYSQL_PORT=33068
MYSQL_USER=root
MYSQL_PASSWORD=J#fe9Yu{
MYSQL_DATABASE=promax_db

# ==========================================
# Redis配置
# ==========================================
REDIS_HOST=**************
REDIS_PORT=63790
REDIS_PASSWORD=
REDIS_KEY_PREFIX=promax_yitaiyitai_com:
REDIS_DEFAULT_EXPIRE=600
REDIS_DB=0

# ==========================================
# 应用通用配置
# ==========================================
NODE_ENV=production
PYTHONPATH=/app/python-mining-service

# ==========================================
# NestJS Server 配置
# ==========================================
NESTJS_INTERNAL_PORT=3003
JWT_SECRET=promax-production-jwt-secret-2024
JWT_EXPIRES_IN=30d
PYTHON_MINING_SERVICE_URL=http://localhost:8000

# API配置
API_PREFIX=api
API_VERSION=v1

# CORS配置
CORS_ORIGIN=http://*************,http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# 文件上传配置
UPLOAD_MAX_SIZE=100MB
UPLOAD_ALLOWED_TYPES=csv,xlsx,xls,json
UPLOAD_PATH=/app/uploads

# ==========================================
# Python Mining Service 配置
# ==========================================
HOST=0.0.0.0
DEBUG=false

# 性能配置
MAX_WORKERS=4
MAX_MEMORY_USAGE_PERCENT=80.0
BATCH_SIZE=1000
GPU_MEMORY_FRACTION=0.8

# 算法配置
MIN_FREQUENCY=2
MIN_CONFIDENCE=0.1
MAX_PATTERN_LENGTH=5
ENABLE_GPU_ACCELERATION=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/mining_service.log

# CORS配置 (多个域名用逗号分隔)
ALLOWED_ORIGINS=http://*************,http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001

# ==========================================
# Nuxt3 Frontend 配置
# ==========================================
# Nuxt 使用标准环境变量
NUXT_HOST=0.0.0.0
NUXT_PORT=3000
NUXT_PUBLIC_API_BASE=/

# ==========================================
# Python Mining Service 配置
# ==========================================
PYTHON_SERVICE_INTERNAL_PORT=8000

# ==========================================
# Docker 容器端口映射 (外部端口)
# ==========================================
NGINX_PORT=3100
NESTJS_EXTERNAL_PORT=3101
PYTHON_SERVICE_EXTERNAL_PORT=3102
NUXT_INTERNAL_PORT=3000

# ==========================================
# 数据卷挂载路径
# ==========================================
DATA_ROOT=~/promax-data
LOGS_DIR=~/promax-data/logs
UPLOADS_DIR=~/promax-data/uploads
REPORTS_DIR=~/promax-data/reports
RESULT_DATA_DIR=~/promax-data/result-data

# ==========================================
# 健康检查配置
# ==========================================
HEALTH_CHECK_TIMEOUT=30
STARTUP_WAIT_TIME=20
SERVICE_CHECK_INTERVAL=10
