ALIACCESSKEYID=LTAI4GDNf4ut1x4r9ND8dcNc
ALIACCESSKEYSECRET=******************************
ALIUPLOADARN=
API_HOST=/api
APP_HOST=0.0.0.0
APP_LIMIT=
BPMAX_API_HOST=
BPMAX_ENGINE_SERVER=http://localhost:10001/api
BPMAX_SERVER=http://127.0.0.1:10003/bpmax
B_KAFKA_BROKERS=**************:9092
B_KAFKA_CLIENT_ID=bpmax-huanpingtong
CONNECTION_LIMIT=100
CRYPTO_KEY=CHABAIDAOBPMAXSS
DEFAULT_WORKSPACE=default
DOMAIN=cbdsplitsubdev.yitaiyitai.com
ENV_MYSQL_VERSION=5.0
ENV_TAG=subbusiness
ES_NAMESPACE=cbdsplitsubdev_yitaiyitai_com
ES_SERVER=http://**************:9200
GLOBAL_WATER_MARK=cbdsplitsubdev_y
KAFKA_BROKERS=**************:9092
KAFKA_CLIENT_ID=bpmax-mysql-es
LOGGER=true
MYSQL_DATABASE=cbd_service_split_sub_dev
MYSQL_HOST=**************
MYSQL_PASSWORD=J#fe9Yu{
MYSQL_PORT=33060
MYSQL_TYPE=mysql
MYSQL_USER=root
NODEHOST=**********
NODE_TLS_REJECT_UNAUTHORIZED=0
OPEN_API_SECRET_KEY=cbdsplitsubdev_yitaiyitai_com_^uijfrhusd@asdiutypmshr2136f
OPEN_APP_ID=cbdsplitsubdev_yitaiyitai_com7hf48f94=_3u
OPEN_APP_SECRET=cbdsplitsubdev_yitaiyitai_comhff9487y3498-=
OPEN_CORPORATION_ID=cbd
OSSBUCKET=pw-garden
OSSHOST=pw-garden.oss-cn-shanghai-internal.aliyuncs.com
OSSREEGION=oss-cn-shanghai
OSSUPLOADBUCKET=pw-garden
OSS_UPLOAD_PUBLIC=0
PLUGIN_OSS_BASE=/cbdsplitsubdev_yitaiyitai_com
PROTOCOL=https
PUBLICPATH_MOBILE=/bpmax_statics/cbdsplitsubdev_yitaiyitai_com/mobile
PUBLICPATH_PC=/bpmax_statics/cbdsplitsubdev_yitaiyitai_com/pc
REDIS_DEFAULT_EXPIRE=600
REDIS_HOST=**************
REDIS_KEY_PREFIX=cbdsplitsubdev_yitaiyitai_com:
REDIS_PASSWORD=
REDIS_PORT=63790
RUNTIME_ENV=test
SECRET_KEY=cbddev_yitaiyitai_com_uisdhjuyewrewrmnc^8sdjk
SERVER_TYPE=cluster,api
SERVER_TYPE__AUTO__=cluster,automation
SESSION_REDIS_KEY_PREFIX=serversplit:
SHOWI18N=
SIGNNAME=
SMSCODE=
SYNC_GROUP_USER_TOPIC_PREFIX=cbdsplit
WXAPPID=wx7ef9bd0c1b39d8e4
WXAPPSECRET=8c68fa4d9527cff193503f3559707261
containerName=cbdsplitsubdev_yitaiyitai_com
mode=production
port=24333
rootPath=/user/app/cbdsplitsubdev_yitaiyitai_com
rootPath__AUTO__=/user/app/cbdsplitsubdev_yitaiyitai_com_auto
WORKER_COUNT_AUTOMATION=4
topic_config__project_auto_replay__partitionsConsumedConcurrently=2
topic_config__project_auto_replay__maxBytesPerPartition=3072
