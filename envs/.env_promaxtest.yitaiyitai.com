# ProMax Production Environment Configuration
# Generated based on load-env.sh requirements and config/production.env values
# Updated with all environment variables from scripts/load-env.sh

# ===========================================
# 服务器配置 (Server Configuration)
# ===========================================
CONTAINER_NAME=promax-platform
IMAGE_NAME=promax-platform
IMAGE_TAG=latest
REMOTE_DIR=/home/<USER>/promax-deployment
REMOTE_DEPLOY_DIR=/home/<USER>/promax-deployment

# ===========================================
# Git 配置 (Git Configuration)
# ===========================================
GIT_REPO_URL=http://gitlabbot:<EMAIL>/bpmax/promax.git
GIT_CREDENTIALS_ID=f75a9928-9b05-4dd7-8fdf-57d80e312ddf

# ===========================================
# Docker 镜像配置 (Docker Image Configuration)
# ===========================================
REGISTRY_URL=registry.yitaiyitai.com

# ===========================================
# 应用通用配置 (Application General)
# ===========================================
NODE_ENV=production
PYTHONPATH=/app/python-mining-service
HOST=0.0.0.0
DEBUG=false

# ===========================================
# 数据库配置 (Database Configuration)
# ===========================================
DB_HOST=**************
DB_PORT=33068
DB_USERNAME=root
DB_PASSWORD=J#fe9Yu{
DB_DATABASE=promax_db
DB_TYPE=mysql

# MySQL 配置 (Python Service)
MYSQL_HOST=**************
MYSQL_PORT=33068
MYSQL_USER=root
MYSQL_PASSWORD=J#fe9Yu{
MYSQL_DATABASE=promax_db

# ===========================================
# Redis 配置 (Redis Configuration)
# ===========================================
REDIS_HOST=**************
REDIS_PORT=63790
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=promax_yitaiyitai_com:
REDIS_DEFAULT_EXPIRE=600

# ===========================================
# JWT 配置 (JWT Configuration)
# ===========================================
JWT_SECRET=promax-production-jwt-secret-2024
JWT_EXPIRES_IN=30d

# ===========================================
# NestJS 服务器配置 (NestJS Server)
# ===========================================
NESTJS_INTERNAL_PORT=3003
NESTJS_EXTERNAL_PORT=3101
API_PREFIX=api
API_VERSION=v1
CORS_ORIGIN=http://*************,http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true
UPLOAD_MAX_SIZE=100MB
UPLOAD_ALLOWED_TYPES=csv,xlsx,xls,json
UPLOAD_PATH=/app/uploads

# ===========================================
# Python 挖掘服务配置 (Python Mining Service)
# ===========================================
PYTHON_MINING_SERVICE_URL=http://localhost:8000
PYTHON_SERVICE_INTERNAL_PORT=8000
PYTHON_SERVICE_PORT=8000
PYTHON_SERVICE_EXTERNAL_PORT=3102

# 性能配置 (Performance)
MAX_WORKERS=4
ENABLE_GPU_ACCELERATION=true
MAX_MEMORY_USAGE_PERCENT=80.0
GPU_MEMORY_FRACTION=0.8

# 算法配置 (Algorithm)
BATCH_SIZE=1000
MIN_FREQUENCY=2
MIN_CONFIDENCE=0.1
MAX_PATTERN_LENGTH=5

# 日志配置 (Logging)
LOG_LEVEL=INFO
LOG_FILE=logs/mining_service.log

# CORS 配置 (Python Service)
ALLOWED_ORIGINS=http://*************,http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001

# ===========================================
# Nuxt3 前端配置 (Nuxt3 Frontend)
# ===========================================
NUXT_HOST=0.0.0.0
NUXT_PORT=3000
NUXT_INTERNAL_PORT=3000
NUXT_PUBLIC_API_BASE=/

# ===========================================
# Docker 端口映射 (Docker Port Mapping)
# ===========================================
NGINX_PORT=3100

# ===========================================
# 数据卷挂载路径 (Data Volume Mount Paths)
# ===========================================
DATA_ROOT=~/promax-data
LOGS_DIR=~/promax-data/logs
UPLOADS_DIR=~/promax-data/uploads
REPORTS_DIR=~/promax-data/reports
RESULT_DATA_DIR=~/promax-data/result-data

# ===========================================
# 健康检查配置 (Health Check)
# ===========================================
HEALTH_CHECK_TIMEOUT=30
STARTUP_WAIT_TIME=20
SERVICE_CHECK_INTERVAL=10