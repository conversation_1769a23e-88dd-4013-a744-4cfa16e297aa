ALIACCESSKEYID=LTAI5tL2nnFpGfBX52FPQyxZ
ALIACCESSKEYSECRET=******************************
ALIUPLOADARN=
API_HOST=/api
APP_HOST=0.0.0.0
APP_LIMIT=2025-12-31T23:59:00
BPMAX_API_HOST=
BPMAX_ENGINE_SERVER=http://localhost:10001/api
BPMAX_SERVER=http://127.0.0.1:10003/bpmax
B_KAFKA_BROKERS=alikafka-pre-cn-g4t3j72l0001-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-g4t3j72l0001-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-g4t3j72l0001-3-vpc.alikafka.aliyuncs.com:9092
B_KAFKA_CLIENT_ID=bpmax
CONNECTION_LIMIT=220
CRYPTO_KEY=UuNC5hRIVeGKHX8z
DEFAULT_WORKSPACE=
DOMAIN=yygl.shuxinyc.com
DATA_SET_PUBLISH_BATCH_SIZE=1000
ENV_MYSQL_VERSION=5.0
ENV_TAG=main
ES_NAMESPACE=
ES_SERVER=http://*************:9200,http://**************:9200,http://**************:9200
GLOBAL_WATER_MARK=
KAFKA_BROKERS=alikafka-pre-cn-g4t3j72l0001-1-vpc.alikafka.aliyuncs.com:9092&&alikafka-pre-cn-g4t3j72l0001-2-vpc.alikafka.aliyuncs.com:9092&&alikafka-pre-cn-g4t3j72l0001-3-vpc.alikafka.aliyuncs.com:9092
KAFKA_CLIENT_ID=bpmax-mysql-es
LOGGER=
MAX_OLD_SPACE_SIZE_AUTOMATION=6144
MAX_OLD_SPACE_SIZE_API=2048
MYSQL_DATABASE=cbd_bpmax_db
MYSQL_HOST=rm-bp19zz7z7hl0uk5q7.mysql.rds.aliyuncs.com
MYSQL_PASSWORD=B#YV4hdp3
MYSQL_PORT=3306
MYSQL_TYPE=mysql
MYSQL_USER=bpmax_opr
NODEHOST=**********
NODE_TLS_REJECT_UNAUTHORIZED=0
OPEN_API_SECRET_KEY=chabaidao_^asdasd%wasdfpocm@eoopasa23xdsa
OPEN_APP_ID=
OPEN_APP_SECRET=
OPEN_CORPORATION_ID=
OSSBUCKET=fsyyglpt-pro
OSSHOST=fsyyglpt-pro.oss-cn-hangzhou-internal.aliyuncs.com
OSSREEGION=oss-cn-hangzhou
OSSUPLOADBUCKET=fsyyglpt-pro
OSS_UPLOAD_PUBLIC=0
PLUGIN_OSS_BASE=/bpmax_plugins/yygl_shuxinyc_com
PROTOCOL=https
PUBLICPATH_MOBILE=/bpmax_statics/yygl_shuxinyc_com/mobile
PUBLICPATH_PC=/bpmax_statics/yygl_shuxinyc_com/pc
REDIS_DEFAULT_EXPIRE=600
REDIS_HOST=r-bp11mw4uk4ru10ciw3.redis.rds.aliyuncs.com
REDIS_KEY_PREFIX=yygl_shuxinyc_com:
REDIS_PASSWORD=0Cb+PGUsnB3+K
REDIS_PORT=6379
RUNTIME_ENV=cbd_prod
SECRET_KEY=yygl_shuxinyc_comjuyewrewrmnc^8sdjk
SERVER_TYPE=master,cluster,api
SERVER_TYPE__AUTO__=master,cluster,automation
SESSION_REDIS_KEY_PREFIX=CBD_MAIN
SHOWI18N=
SIGNNAME=零蝉智能
SMSCODE=
SYNC_GROUP_USER_TOPIC_PREFIX=
topic_config__project_auto_replay__partitionsConsumedConcurrently=2
topic_config__project_auto_replay__maxBytesPerPartition=5120
WORKER_COUNT_API=4
WORKER_COUNT_AUTOMATION=2
WXAPPID=
WXAPPSECRET=
containerName=yygl_shuxinyc_com
mode=production
port1=10110
port=10108
rootPath=/root/app/yygl_shuxinyc_com
