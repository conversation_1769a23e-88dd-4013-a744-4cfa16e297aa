# 数据库配置
MYSQL_TYPE=mysql
MYSQL_HOST=**************
MYSQL_PORT=33068
MYSQL_USER=root
MYSQL_PASSWORD='J#fe9Yu{'
MYSQL_DATABASE=feishu_jumper_pre

# Redis配置
REDIS_HOST=**************
REDIS_PORT=63790
REDIS_PASSWORD=
REDIS_KEY_PREFIX=feishujumperpre:
REDIS_DEFAULT_EXPIRE=600

# 阿里云OSS配置
ALIACCESSKEYID=xxxxxx
ALIACCESSKEYSECRET=xxxxxx
OSSBUCKET=xxxxxx
OSSREEGION=xxxxxx
OSSHOST=xxxxxx

# 应用配置
PORT=3002
NODE_ENV=development

# Zero Ops 服务系统配置
ZERO_OPS_API_URL=http://**************:18888/api/v1
ZERO_OPS_USERNAME=devops
ZERO_OPS_PASSWORD=3-546-_iuhh5498

DOMAIN=https://feishujumperpre.yitaiyitai.com/


API_SECRET_KEY=lxgt1wnIkxG7gQwrlrB2iFvOuXzbQQ3J
JWT_SECRET=RtDCtd4P2VW9DiS8goDxDGurIxVnpRNQ
