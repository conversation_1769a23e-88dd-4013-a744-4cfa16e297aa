#!/bin/bash

# 解压 release.tar.gz，进入目录
# TODO: DC 情况需要再处理

# 导入环境变量
export $(xargs < .env)

helperbranchparam=$1

if [ -z "$helperbranchparam" ]; then
    # 如果为空，则设置默认分支为 'master'
    helperBranch='master'
else
    # 如果不为空，则将传入的参数赋值给 helperBranch
    helperBranch="$helperbranchparam"
fi

echo "helper-branch: $helperBranch"

# 创建程序目录
mkdir -p ${rootPath}/dist_prod/
mkdir ${rootPath}/clogs
# 拷贝 release.tar.gz 目录内所有文件到程序目录的父级
cp -r . ${rootPath}

# 清理四个项目中指定位置的dist目录
cd ${rootPath}/dist_prod
if [ -d "hpt-frontend/dist" ]; then
    echo "Removing hpt-frontend dist .nuxt directory"
    rm -rf hpt-frontend/dist hpt-frontend/.nuxt
fi
if [ -d "huanpingtong-server/app" ]; then
    echo "Removing all files except plugin_* in huanpingtong-server/app directory"
    find huanpingtong-server/app -depth ! -regex ".*plugin_.*" -delete
fi
if [ -d "bpmax-server/dist" ]; then
    echo "Removing bpmax-server/dist directory"
    rm -rf bpmax-server/dist
fi
if [ -d "hpt-mobile/dist" ]; then
    echo "Removing hpt-mobile/dist directory"
    rm -rf hpt-mobile/dist
fi

# 新程序覆盖
cd ${rootPath}
tar -xzf files.tar.gz -C ./ && rm -rf files.tar.gz

# 如果有镜像，加载镜像
if [ -f "bpmax_prod.tar" ]; then
    echo "Loading bpmax_prod.tar Docker image"
    docker load -i bpmax_prod.tar
    rm -rf bpmax_prod.tar
fi

builderImage="registry.yitaiyitai.com/bpmax/bpmax:{{TAG}}"

if echo $builderImage | grep '{TAG}' ;then 
    tag=$(echo $( docker images | grep "registry.yitaiyitai.com/bpmax/bpmax" | sort -k2 -r | awk -F' ' '{print $2}' | head -n1))
    builderImage="registry.yitaiyitai.com/bpmax/bpmax:$tag"

    sed -i "s/{{TAG}}/$tag/g" ./docker-compose.yml
fi

# 进入应用目录
cd ${rootPath}/dist_prod
echo "Running Docker container"
docker run --name=builder --rm --env-file ../.env \
-e TZ=Asia/Shanghai \
-v $(pwd)/hpt-frontend:/var/www/hpt-frontend \
-v $(pwd)/hpt-mobile:/var/www/hpt-mobile \
-v $(pwd)/huanpingtong-server:/var/www/huanpingtong-server \
-v $(pwd)/bpmax-server:/var/www/bpmax-server \
$builderImage /bin/bash -c "yarn config set registry https://npm.yitaiyitai.com \
&& yarn config set disturl https://npmmirror.com/mirrors/node \
&& yarn config set network-timeout 100000 \
&& yarn cache clean \
&& unset NODE_TLS_REJECT_UNAUTHORIZED \
&& mkdir -p /usr/local/share/.cache/yarn && chmod -R 777 /usr/local/share/.cache/yarn \
&& cd hpt-frontend && rm -rf node_modules && yarn --prod \
&& cd ../huanpingtong-server && rm -rf node_modules && yarn --prod \
&& ls -l /usr/local/share/.cache/yarn/ \
&& rm -rf node_modules/@bpmax/helper && git clone -b ${helperBranch} http://oauth2:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper \
&& yarn migrate_prod up \
&& cd ../bpmax-server && rm -rf node_modules && yarn \
&& cat node_modules/@bpmax/helper/package.json \
&& rm -rf node_modules/@bpmax/helper && git clone -b ${helperBranch} http://oauth2:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper \
&& cat node_modules/@bpmax/helper/package.json \
"

# 清理容器
docker rm -f builder

# 修复存在问题的三方包依赖
cd ${rootPath}/dist_prod/huanpingtong-server
sed -i 's/process.exit(a)//' node_modules/lz4-asm/dist/lz4wasm.js
sed -i 's/if(!(a instanceof z))throw a;//' node_modules/lz4-asm/dist/lz4wasm.js

if ! [ $(command -v docker-compose) ]; then
    echo "add docker-compose"
    alias docker-compose='docker compose'
fi

cd ${rootPath}
docker-compose stop -t 10 bpmax
docker-compose rm -f bpmax
docker-compose up --no-recreate -d

# 自动化容器
auto=`cat .env | grep "SERVER_TYPE__AUTO__="`
if [ -n "$auto" ]; then
    echo "Automation deployment"

    if [ ! -d "${rootPath__AUTO__}" ]; then
        echo "Creating ${rootPath__AUTO__}"
        mkdir -p ${rootPath__AUTO__}
    fi
    cp .env ${rootPath__AUTO__}/.env
    cp docker-compose.yml ${rootPath__AUTO__}/docker-compose.yml
    cd ${rootPath__AUTO__}
    sed -i '/^ *ports:/,/^ *- "${port}:8080"/d' ./docker-compose.yml
    sed -i 's/SERVER_TYPE__AUTO__=/SERVER_TYPE=/' ./.env
    docker-compose down
    docker-compose up --no-recreate -d
fi

