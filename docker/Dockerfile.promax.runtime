# ProMax 运行时基础镜像
# 包含 Node.js 22、Python 3.12、ngin<PERSON>、supervisor 等运行时环境

FROM docker.m.daocloud.io/library/python:3.12-slim

# 安装Node.js和系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    gnupg \
    supervisor \
    nginx \
    netcat-openbsd \
    && curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# 安装yarn
RUN npm install -g yarn && \
    yarn --version && \
    echo "✅ Yarn installed successfully"

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装PyTorch (CPU版本，支持多平台)
RUN pip install --no-cache-dir \
    torch>=2.0.0 \
    torchvision>=0.15.0 \
    torchaudio>=2.0.0

# 安装Python基础依赖（常用的包）
RUN pip install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    pydantic==2.5.0 \
    pydantic-settings==2.1.0 \
    pymysql==1.1.0 \
    sqlalchemy==2.0.23 \
    redis==5.0.1 \
    pm4py==******** \
    loguru==0.7.2 \
    httpx==0.25.2 \
    aiohttp==3.9.1 \
    pandas==2.3.1 \
    numpy==1.26.4 \
    scipy==1.13.1 \
    scikit-learn==1.5.1 \
    networkx==3.3 \
    psutil==6.0.0 \
    memory-profiler==0.61.0 \
    numba>=0.58.0 \
    python-igraph>=0.11.0 \
    joblib>=1.3.0 \
    orjson>=3.9.0 \
    msgpack>=1.0.0

# 创建应用目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads /app/reports /app/result-data /var/log/supervisor

# 复制配置文件模板
# COPY docker/nginx.conf /etc/nginx/nginx.conf
# COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
# COPY docker/start-mount.sh /app/start.sh

# 设置权限
# RUN chmod +x /app/start.sh

# 暴露端口
# EXPOSE 80 3003 8000

# 启动命令
# CMD ["/app/start.sh"]
