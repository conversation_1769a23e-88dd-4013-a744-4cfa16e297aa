#!/bin/bash

# ProMax 挂载模式启动脚本
# 适用于代码通过volume挂载的部署方式

set -e

echo "🚀 ProMax 流程挖掘平台启动 (挂载模式)"
echo "====================================="

# 检查挂载的代码目录
if [ ! -d "/app/code" ]; then
    echo "❌ 错误: 代码目录 /app/code 未挂载"
    exit 1
fi

echo "✅ 代码目录挂载正常: /app/code"

# 创建符号链接到挂载的代码
echo "🔗 创建代码符号链接..."
ln -sf /app/code/client /app/client
ln -sf /app/code/server /app/server
ln -sf /app/code/python-mining-service /app/python-mining-service
ln -sf /app/code/config /app/config
ln -sf /app/code/scripts /app/scripts

echo "✅ 符号链接创建完成"
echo "检查符号链接:"
ls -la /app/ | grep -E "(client|server|python-mining-service)"

# 检查构建产物
echo "🔍 检查构建产物..."

# 检查前端构建
echo "🔍 检查前端构建产物..."
if [ ! -d "/app/client/.output" ]; then
    echo "❌ 前端构建产物不存在: /app/client/.output"
    echo "当前client目录结构:"
    ls -la /app/client/
    echo "检查挂载的代码目录:"
    ls -la /app/code/client/
    echo "请确保在服务器上执行了 'cd client && yarn build'"
    exit 1
fi
echo "✅ 前端构建产物存在"

# 检查后端构建
echo "🔍 检查后端构建产物..."
echo "检查符号链接: /app/server -> $(readlink /app/server)"
echo "检查挂载的代码目录: /app/code/server/dist"

if [ -d "/app/code/server/dist" ]; then
    echo "✅ 挂载代码中的dist目录存在"
    echo "dist目录内容:"
    ls -la /app/code/server/dist/
else
    echo "❌ 挂载代码中的dist目录不存在"
    echo "server目录结构:"
    ls -la /app/code/server/
fi

if [ ! -f "/app/server/dist/src/main.js" ]; then
    echo "❌ 后端构建产物不存在: /app/server/dist/src/main.js"
    echo "检查符号链接是否正确:"
    ls -la /app/server/
    echo "检查实际挂载目录:"
    ls -la /app/code/server/dist/
    echo "检查dist目录结构:"
    find /app/code/server/dist/ -name "*.js" 2>/dev/null | head -10
    echo "请确保在服务器上执行了 'cd server && yarn build'"
    exit 1
fi
echo "✅ 后端构建产物存在: /app/server/dist/src/main.js"

# 检查Python服务
echo "🔍 检查Python服务文件..."
if [ ! -f "/app/python-mining-service/app/main.py" ]; then
    echo "❌ Python服务文件不存在: /app/python-mining-service/app/main.py"
    echo "检查python-mining-service目录:"
    ls -la /app/python-mining-service/
    echo "检查挂载的代码目录:"
    ls -la /app/code/python-mining-service/
    exit 1
fi
echo "✅ Python服务文件存在"

# 加载环境变量
if [ -f "/app/config/production.env" ]; then
    echo "📋 加载环境配置..."
    source /app/config/production.env
    echo "✅ 环境配置加载完成"
else
    echo "⚠️  警告: 环境配置文件不存在，使用默认配置"
fi

# 设置权限
echo "🔐 设置目录权限..."
chown -R www-data:www-data /app/uploads /app/reports /app/result-data
chmod -R 755 /app/uploads /app/reports /app/result-data

# 等待数据库连接
echo "⏳ 等待数据库连接..."
until nc -z ${DB_HOST:-${MYSQL_HOST}} ${DB_PORT:-${MYSQL_PORT}}; do
    echo "等待MySQL数据库 ${DB_HOST:-${MYSQL_HOST}}:${DB_PORT:-${MYSQL_PORT}}..."
    sleep 2
done
echo "✅ 数据库连接成功"

# 等待Redis连接
echo "⏳ 等待Redis连接..."
until nc -z ${REDIS_HOST} ${REDIS_PORT:-6379}; do
    echo "等待Redis ${REDIS_HOST}:${REDIS_PORT:-6379}..."
    sleep 2
done
echo "✅ Redis连接成功"

# 检查数据库迁移
echo "🔄 检查数据库迁移..."
cd /app/server
if [ -d "dist" ] && [ -f "package.json" ]; then
    # 这里可以添加数据库迁移检查逻辑
    echo "✅ 数据库迁移检查完成"
fi

# 检查Python环境
echo "🐍 检查Python环境..."
cd /app/python-mining-service
python -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import torch
    print(f'PyTorch版本: {torch.__version__}')
    
    import pandas as pd
    print(f'Pandas版本: {pd.__version__}')
    
    import pm4py
    print(f'PM4Py版本: {pm4py.__version__}')
    
    print('✅ Python环境检查完成')
except ImportError as e:
    print(f'⚠️  Python依赖警告: {e}')
"

# 检查GPU支持
echo "🔍 检查GPU支持..."
python -c "
try:
    import torch
    if torch.backends.mps.is_available():
        print('✅ 苹果M芯片GPU (MPS) 可用')
    elif torch.cuda.is_available():
        print('✅ NVIDIA CUDA GPU 可用')
    else:
        print('⚠️  仅CPU模式')
except:
    print('⚠️  GPU检查失败，使用CPU模式')
"

# 启动所有服务
echo "🎯 启动所有服务..."
cd /app

# 启动supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
