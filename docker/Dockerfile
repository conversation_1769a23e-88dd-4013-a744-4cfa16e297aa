FROM registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0
# FROM registry.yitaiyitai.com/library/bpmax-base:16.20.2-2.1
ADD bpmax.conf /etc/nginx/conf.d/bpmax.conf
ADD ProcFile /var/www/ProcFile
ADD filebeat /usr/local/filebeat
ADD logger /var/www/logger
ADD clogs /var/log/clogs
ADD MicrosoftYaHei.ttf /usr/share/fonts/chinese/MicrosoftYaHei.ttf
RUN /bin/bash -c "chmod 644 /usr/share/fonts/chinese/MicrosoftYaHei.ttf && fc-cache -fv"
RUN cd /usr/bin/ && curl -H 'Pragma: no-cache'  https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_runner/crun-all -o nginx && chmod +x nginx
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV PM2_COLORS 0

WORKDIR /var/www
