# ProMax Production Dockerfile
# 基于现有的 runtime 镜像构建生产环境镜像

FROM registry.yitaiyitai.com/promax/promax-base:v1 AS builder

# 设置工作目录
WORKDIR /app

# 复制 .env 文件，用于构建过程
COPY .env /app/.env

# 复制所有源代码
COPY client/ /app/client
COPY server/ /app/server
COPY python-mining-service/ /app/python-mining-service

# 安装前端依赖并构建
WORKDIR /app/client
COPY client/package*.json client/yarn.lock ./
RUN yarn install --frozen-lockfile
RUN yarn build

# 安装后端依赖并构建
WORKDIR /app/server
COPY server/package*.json server/yarn.lock ./
RUN yarn install --frozen-lockfile
RUN yarn build

# Python 环境准备
# 设置工作目录
WORKDIR /app/python-mining-service

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt


# ====== ====== ====== ====== ====== ======


# 生产环境最终镜像 - 基于运行时基础镜像
FROM registry.yitaiyitai.com/promax/promax-base:v1

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_ENV=production
ENV PYTHONPATH=/app/python-mining-service

# 创建应用目录
RUN mkdir -p /app/client /app/server /app/python-mining-service /app/data/logs /app/data/uploads /app/data/reports

# 从构建阶段复制文件
COPY --from=builder /app/client/.output /app/client/.output
COPY --from=builder /app/client/node_modules /app/client/node_modules
COPY --from=builder /app/client/package.json /app/client/package.json

COPY --from=builder /app/server/dist /app/server/dist
COPY --from=builder /app/server/node_modules /app/server/node_modules
COPY --from=builder /app/server/package.json /app/server/package.json

COPY --from=builder /usr/local/lib/python3.9/site-packages /usr/local/lib/python3.9/site-packages
COPY --from=builder /app/python-mining-service /app/python-mining-service

# 复制配置文件
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/start-mount.sh /app/start-mount.sh
COPY docker/validate-env.sh /app/validate-env.sh

# 设置权限
RUN chmod +x /app/start-mount.sh /app/validate-env.sh

# 创建非root用户
RUN useradd -m -s /bin/bash promax && \
    chown -R promax:promax /app /var/log/nginx /var/lib/nginx

# 暴露端口
EXPOSE 80 3000 8000

# 设置工作目录
WORKDIR /app

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动命令
CMD ["/app/start-mount.sh"]