module_version: 1.0

var:
  - name: input
    default: azure-eventhub
  - name: eventhub
    default: "insights-logs-signinlogs"
  - name: consumer_group
    default: "$Default"
  - name: connection_string
  - name: storage_account
  - name: storage_account_key
  - name: storage_account_container
  - name: resource_manager_endpoint
  - name: tags
    default: [forwarded]

ingest_pipeline:
  - ingest/pipeline.yml
  - ../azure-shared-pipeline.yml
input: config/{{.input}}.yml
