---
description: Pipeline for parsing Linux auditd logs
processors:
- set:
    field: event.ingested
    value: '{{_ingest.timestamp}}'
- grok:
    field: message
    pattern_definitions:
      AUDIT_TYPE: "type=%{NOTSPACE:auditd.log.record_type}"
      AUDIT_NODE: "node=%{IPORHOST:auditd.log.node} "
      AUDIT_PREFIX: "^(?:%{AUDIT_NODE})?%{AUDIT_TYPE} msg=audit\\(%{NUMBER:auditd.log.epoch}:%{NUMBER:auditd.log.sequence}\\):(%{DATA})?"
      AUDIT_KEY_VALUES: "%{WORD}=%{GREEDYDATA}"
      ANY: ".*"
    patterns:
    - "%{AUDIT_PREFIX} %{AUDIT_KEY_VALUES:auditd.log.kv} old auid=%{NUMBER:auditd.log.old_auid}
      new auid=%{NUMBER:auditd.log.new_auid} old ses=%{NUMBER:auditd.log.old_ses}
      new ses=%{NUMBER:auditd.log.new_ses}"
    - "%{AUDIT_PREFIX} %{AUDIT_KEY_VALUES:auditd.log.kv} msg=['\"]([^=]*\\s)?%{ANY:auditd.log.sub_kv}['\"]"
    - "%{AUDIT_PREFIX} %{AUDIT_KEY_VALUES:auditd.log.kv}"
    - "%{AUDIT_PREFIX}"
    - "%{AUDIT_TYPE} %{AUDIT_KEY_VALUES:auditd.log.kv}"
- kv:
    field: auditd.log.kv
    field_split: '\s(?![\w\"]+?(\s+|$))'
    value_split: '(?<!\\)='
    target_field: auditd.log
- kv:
    field: auditd.log.sub_kv
    field_split: "\\s+(?=[^\\s]+=)"
    value_split: "="
    target_field: auditd.log
    ignore_missing: true
- rename:
    field: message
    target_field: event.original
    ignore_failure: true
- date:
    field: auditd.log.epoch
    target_field: "@timestamp"
    formats:
    - UNIX
    ignore_failure: true
- rename:
    ignore_failure: true
    field: auditd.log.old-auid
    target_field: auditd.log.old_auid
- rename:
    ignore_failure: true
    field: auditd.log.old-ses
    target_field: auditd.log.old_ses
- script:
    lang: painless
    source: |
        String trimQuotes(def singleQuote, def doubleQuote, def v) {
            if (v.startsWith(singleQuote) || v.startsWith(doubleQuote)) {
                v = v.substring(1, v.length());
            }
            if (v.endsWith(singleQuote) || v.endsWith(doubleQuote)) {
                v = v.substring(0, v.length()-1);
            }
            return v;
        }

        boolean isHexAscii(String v) {
            def len = v.length();

            if (len == 0 || len % 2 != 0) {
                return false;
            }

            for (int i = 0 ; i < len ; i++) {
                if (Character.digit(v.charAt(i), 16) == -1) {
                    return false;
                }
            }
            return true;
        }

        String convertHexToString(String hex) {
            StringBuilder sb = new StringBuilder();
            boolean needed_encoding = false;

            for (int i=0; i < hex.length() - 1; i+=2) {
                int cp = Integer.parseInt(hex.substring(i, (i +2)), 16);
                if (cp < 33 || cp == 34 || cp == 127) {
                    needed_encoding = true;
                }
                if (cp < 32 || cp == 127) {
                    sb.append('^');
                    cp ^= 64;
                }
                sb.append((char)cp);
            }
            if (needed_encoding) {
                return sb.toString();
            }
            return hex;
        }

        def possibleHexKeys = ["exe", "cmd", "data", "path", "comm", "file", "name", "watch", "cwd", "acct", "dir", "vm", "old-chardev", "new-chardev", "old-disk", "new-disk", "old-fs", "new-fs", "old-net", "new-net", "device", "cgroup", "apparmor", "operation", "denied_mask", "info", "profile", "requested_mask", "old-rng", "new-rng", "ocomm", "grp", "new_group", "invalid_context", "sw", "root_dir", "proctitle"];
        def audit = ctx.auditd.get("log");
        Iterator entries = audit.entrySet().iterator();

        while (entries.hasNext()) {
            def e = entries.next();
            def k = e.getKey();
            def v = e.getValue();

            // Remove entries whose value is ?
            if (v == "?" || v == "(null)" || v == "") {
                entries.remove();
                continue;
            }

            // Convert hex values to ASCII.
            if (possibleHexKeys.contains(k) && isHexAscii(v)) {
                v = convertHexToString(v);
                audit.put(k, v);
            }

            // Trim quotes.
            if (v instanceof String) {
                v = trimQuotes(params.single_quote, params.double_quote, v);
                audit.put(k, v);
            }

            // Convert arch.
            if (k == "arch" && v == "c000003e") {
                audit.put(k, "x86_64");
            }
        }
    params:
      single_quote: "'"
      double_quote: "\""
- convert:
    field: auditd.log.sequence
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.lport
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.rport
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.entries
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.dst_prefixlen
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.ksize
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.size
    type: long
    ignore_missing: true
- convert:
    field: auditd.log.src_prefixlen
    type: long
    ignore_missing: true
- set:
    field: event.kind
    value: event
- script:
    lang: painless
    ignore_failure: true
    # Auditd record type to ECS mappings
    # AUTOGENERATED FROM go-libaudit v2.2.0, DO NOT EDIT
    params:
      syscalls:
        '*':
          - event:
              category:
                - process
              type:
                - info
        accept:
          - event:
              action:
                - accepted-connection-from
              category:
                - network
              type:
                - connection
                - start
        accept4:
          - event:
              action:
                - accepted-connection-from
              category:
                - network
              type:
                - connection
                - start
        access:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        adjtimex:
          - event:
              action:
                - changed-system-time
              category:
                - host
              type:
                - change
        bind:
          - event:
              action:
                - bound-socket
              category:
                - network
              type:
                - start
        brk:
          - event:
              action:
                - allocated-memory
              category:
                - process
              type:
                - info
        chmod:
          - event:
              action:
                - changed-file-permissions-of
              category:
                - file
              type:
                - change
        chown:
          - event:
              action:
                - changed-file-ownership-of
              category:
                - file
              type:
                - change
        clock_settime:
          - event:
              action:
                - changed-system-time
              category:
                - host
              type:
                - change
        connect:
          - event:
              action:
                - connected-to
              category:
                - network
              type:
                - connection
                - start
        creat:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - creation
        delete_module:
          - event:
              action:
                - unloaded-kernel-module
              category:
                - driver
              type:
                - end
        execve:
          - event:
              action:
                - executed
              category:
                - process
              type:
                - start
        execveat:
          - event:
              action:
                - executed
              category:
                - process
              type:
                - start
        faccessat:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        fallocate:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - change
        fchmod:
          - event:
              action:
                - changed-file-permissions-of
              category:
                - file
              type:
                - change
        fchmodat:
          - event:
              action:
                - changed-file-permissions-of
              category:
                - file
              type:
                - change
        fchown:
          - event:
              action:
                - changed-file-ownership-of
              category:
                - file
              type:
                - change
        fchownat:
          - event:
              action:
                - changed-file-ownership-of
              category:
                - file
              type:
                - change
        fgetxattr:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        finit_module:
          - event:
              action:
                - loaded-kernel-module
              category:
                - driver
              type:
                - start
        fremovexattr:
          - event:
              action:
                - changed-file-attributes-of
              category:
                - file
              type:
                - change
        fsetxattr:
          - event:
              action:
                - changed-file-attributes-of
              category:
                - file
              type:
                - change
        fstat:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        fstatat:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        fstatfs:
          - event:
              action:
                - checked-filesystem-metadata-of
              category:
                - file
              type:
                - info
        ftruncate:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - change
        futimens:
          - event:
              action:
                - changed-timestamp-of
              category:
                - file
              type:
                - info
        futimesat:
          - event:
              action:
                - changed-timestamp-of
              category:
                - file
              type:
                - info
        getxattr:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        init_module:
          - event:
              action:
                - loaded-kernel-module
              category:
                - driver
              type:
                - start
        kill:
          - event:
              action:
                - killed-pid
              category:
                - process
              type:
                - end
        lchown:
          - event:
              action:
                - changed-file-ownership-of
              category:
                - file
              type:
                - change
        lgetxattr:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        listen:
          - event:
              action:
                - listen-for-connections
              category:
                - network
              type:
                - start
        lremovexattr:
          - event:
              action:
                - changed-file-attributes-of
              category:
                - file
              type:
                - change
        lsetxattr:
          - event:
              action:
                - changed-file-attributes-of
              category:
                - file
              type:
                - change
        lstat:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        mkdir:
          - event:
              action:
                - created-directory
              category:
                - file
              type:
                - creation
        mkdirat:
          - event:
              action:
                - created-directory
              category:
                - file
              type:
                - creation
        mknod:
          - event:
              action:
                - make-device
              category:
                - file
              type:
                - creation
        mknodat:
          - event:
              action:
                - make-device
              category:
                - file
              type:
                - creation
        mmap:
          - event:
              action:
                - allocated-memory
              category:
                - process
              type:
                - info
        mmap2:
          - event:
              action:
                - allocated-memory
              category:
                - process
              type:
                - info
        mount:
          - event:
              action:
                - mounted
              category:
                - file
              type:
                - creation
        newfstatat:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        open:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - info
        openat:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - info
        read:
          - event:
              action:
                - read-file
              category:
                - file
              type:
                - info
        readlink:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - info
        readlinkat:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - info
        recv:
          - event:
              action:
                - received-from
              category:
                - network
              type:
                - connection
                - info
        recvfrom:
          - event:
              action:
                - received-from
              category:
                - network
              type:
                - connection
                - info
        recvmmsg:
          - event:
              action:
                - received-from
              category:
                - network
              type:
                - connection
                - info
        recvmsg:
          - event:
              action:
                - received-from
              category:
                - network
              type:
                - connection
                - info
        removexattr:
          - event:
              action:
                - changed-file-attributes-of
              category:
                - file
              type:
                - change
        rename:
          - event:
              action:
                - renamed
              category:
                - file
              type:
                - change
        renameat:
          - event:
              action:
                - renamed
              category:
                - file
              type:
                - change
        renameat2:
          - event:
              action:
                - renamed
              category:
                - file
              type:
                - change
        rmdir:
          - event:
              action:
                - deleted
              category:
                - file
              type:
                - deletion
        sched_setattr:
          - event:
              action:
                - adjusted-scheduling-policy-of
              category:
                - process
              type:
                - change
        sched_setparam:
          - event:
              action:
                - adjusted-scheduling-policy-of
              category:
                - process
              type:
                - change
        sched_setscheduler:
          - event:
              action:
                - adjusted-scheduling-policy-of
              category:
                - process
              type:
                - change
        send:
          - event:
              action:
                - sent-to
              category:
                - network
              type:
                - connection
                - info
        sendmmsg:
          - event:
              action:
                - sent-to
              category:
                - network
              type:
                - connection
                - info
        sendmsg:
          - event:
              action:
                - sent-to
              category:
                - network
              type:
                - connection
                - info
        sendto:
          - event:
              action:
                - sent-to
              category:
                - network
              type:
                - connection
                - info
        setdomainname:
          - event:
              action:
                - changed-system-name
              category:
                - host
              type:
                - change
        setegid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        seteuid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setfsgid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setfsuid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setgid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        sethostname:
          - event:
              action:
                - changed-system-name
              category:
                - host
              type:
                - change
        setregid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setresgid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setresuid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setreuid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        settimeofday:
          - event:
              action:
                - changed-system-time
              category:
                - host
              type:
                - change
        setuid:
          - event:
              action:
                - changed-identity-of
              category:
                - process
              type:
                - change
        setxattr:
          - event:
              action:
                - changed-file-attributes-of
              category:
                - file
              type:
                - change
        stat:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        stat64:
          - event:
              action:
                - checked-metadata-of
              category:
                - file
              type:
                - info
        statfs:
          - event:
              action:
                - checked-filesystem-metadata-of
              category:
                - file
              type:
                - info
        stime:
          - event:
              action:
                - changed-system-time
              category:
                - host
              type:
                - change
        symlink:
          - event:
              action:
                - symlinked
              category:
                - file
              type:
                - creation
        symlinkat:
          - event:
              action:
                - symlinked
              category:
                - file
              type:
                - creation
        tgkill:
          - event:
              action:
                - killed-pid
              category:
                - process
              type:
                - end
        tkill:
          - event:
              action:
                - killed-pid
              category:
                - process
              type:
                - end
        truncate:
          - event:
              action:
                - opened-file
              category:
                - file
              type:
                - change
        umount:
          - event:
              action:
                - unmounted
              category:
                - file
              type:
                - deletion
        umount2:
          - event:
              action:
                - unmounted
              category:
                - file
              type:
                - deletion
        unlink:
          - event:
              action:
                - deleted
              category:
                - file
              type:
                - deletion
        unlinkat:
          - event:
              action:
                - deleted
              category:
                - file
              type:
                - deletion
        utime:
          - event:
              action:
                - changed-timestamp-of
              category:
                - file
              type:
                - info
        utimensat:
          - event:
              action:
                - changed-timestamp-of
              category:
                - file
              type:
                - info
        utimes:
          - event:
              action:
                - changed-timestamp-of
              category:
                - file
              type:
                - info
        write:
          - event:
              action:
                - wrote-to-file
              category:
                - file
              type:
                - change
      types:
        ACCT_LOCK:
          - event:
              action:
                - locked-account
              category:
                - iam
              type:
                - user
                - info
        ACCT_UNLOCK:
          - event:
              action:
                - unlocked-account
              category:
                - iam
              type:
                - user
                - info
        ADD_GROUP:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
              - from:
                  - id
                  - acct
                to: group
            event:
              action:
                - added-group-account-to
              category:
                - iam
              type:
                - group
                - creation
        ADD_USER:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
              - from:
                  - id
                  - acct
                to: user.target
            event:
              action:
                - added-user-account
              category:
                - iam
              type:
                - user
                - creation
        ANOM_ABEND:
          - event:
              action:
                - crashed-program
              category:
                - process
              type:
                - end
        ANOM_EXEC:
          - event:
              action:
                - attempted-execution-of-forbidden-program
              category:
                - process
              type:
                - start
        ANOM_LINK:
          - event:
              action:
                - used-suspicious-link
        ANOM_LOGIN_FAILURES:
          - event:
              action:
                - failed-log-in-too-many-times-to
        ANOM_LOGIN_LOCATION:
          - event:
              action:
                - attempted-log-in-from-unusual-place-to
        ANOM_LOGIN_SESSIONS:
          - event:
              action:
                - opened-too-many-sessions-to
        ANOM_LOGIN_TIME:
          - event:
              action:
                - attempted-log-in-during-unusual-hour-to
        ANOM_PROMISCUOUS:
          - event:
              action:
                - changed-promiscuous-mode-on-device
        ANOM_RBAC_INTEGRITY_FAIL:
          - event:
              action:
                - tested-file-system-integrity-of
        AVC:
          - event:
              action:
                - violated-selinux-policy
            has_fields:
              - seresult
          - event:
              action:
                - violated-apparmor-policy
            has_fields:
              - apparmor
        CHGRP_ID:
          - event:
              action:
                - changed-group
              category:
                - process
              type:
                - change
        CHUSER_ID:
          - event:
              action:
                - changed-user-id
              category:
                - process
              type:
                - change
        CONFIG_CHANGE:
          - event:
              action:
                - changed-audit-configuration
              category:
                - process
                - configuration
              type:
                - change
        CRED_ACQ:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - acquired-credentials
              category:
                - authentication
              type:
                - info
        CRED_DISP:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - disposed-credentials
              category:
                - authentication
              type:
                - info
        CRED_REFR:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - refreshed-credentials
              category:
                - authentication
              type:
                - info
        CRYPTO_KEY_USER:
          - event:
              action:
                - negotiated-crypto-key
              category:
                - process
              type:
                - info
        CRYPTO_LOGIN:
          - event:
              action:
                - crypto-officer-logged-in
        CRYPTO_LOGOUT:
          - event:
              action:
                - crypto-officer-logged-out
              category:
                - process
              type:
                - info
        CRYPTO_SESSION:
          - event:
              action:
                - started-crypto-session
              category:
                - process
              type:
                - info
        DAC_CHECK:
          - event:
              action:
                - access-result
        DAEMON_ABORT:
          - event:
              action:
                - aborted-auditd-startup
              category:
                - process
              type:
                - stop
        DAEMON_ACCEPT:
          - event:
              action:
                - remote-audit-connected
              category:
                - network
              type:
                - connection
                - start
        DAEMON_CLOSE:
          - event:
              action:
                - remote-audit-disconnected
              category:
                - network
              type:
                - connection
                - start
        DAEMON_CONFIG:
          - event:
              action:
                - changed-auditd-configuration
              category:
                - process
                - configuration
              type:
                - change
        DAEMON_END:
          - event:
              action:
                - shutdown-audit
              category:
                - process
              type:
                - stop
        DAEMON_ERR:
          - event:
              action:
                - audit-error
              category:
                - process
              type:
                - info
        DAEMON_RECONFIG:
          - event:
              action:
                - reconfigured-auditd
              category:
                - process
                - configuration
              type:
                - info
        DAEMON_RESUME:
          - event:
              action:
                - resumed-audit-logging
              category:
                - process
              type:
                - change
        DAEMON_ROTATE:
          - event:
              action:
                - rotated-audit-logs
              category:
                - process
              type:
                - change
        DAEMON_START:
          - event:
              action:
                - started-audit
              category:
                - process
              type:
                - start
        DEL_GROUP:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
              - from:
                  - id
                  - acct
                to: group
            event:
              action:
                - deleted-group-account-from
              category:
                - iam
              type:
                - group
                - deletion
        DEL_USER:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
              - from:
                  - id
                  - acct
                to: user.target
            event:
              action:
                - deleted-user-account
              category:
                - iam
              type:
                - user
                - deletion
        FEATURE_CHANGE:
          - event:
              action:
                - changed-audit-feature
              category:
                - configuration
              type:
                - change
        FS_RELABEL:
          - event:
              action:
                - relabeled-filesystem
        GRP_AUTH:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
            event:
              action:
                - authenticated-to-group
              category:
                - authentication
              type:
                - info
        GRP_CHAUTHTOK:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
              - from:
                  - acct
                  - id
                  - uid
                to: group
            event:
              action:
                - changed-group-password
              category:
                - iam
              type:
                - group
                - change
        GRP_MGMT:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: group
              - from:
                  - uid
                to: user.effective
            event:
              action:
                - modified-group-account
              category:
                - iam
              type:
                - group
                - change
        KERNEL:
          - event:
              action:
                - initialized-audit-subsystem
              category:
                - process
              type:
                - info
        KERN_MODULE:
          - event:
              action:
                - loaded-kernel-module
              category:
                - driver
              type:
                - start
        LABEL_LEVEL_CHANGE:
          - event:
              action:
                - modified-level-of
        LABEL_OVERRIDE:
          - event:
              action:
                - overrode-label-of
        LOGIN:
          - copy:
              - from:
                  - old_auid
                  - old-auid
                to: user
              - from:
                  - new-auid
                  - new_auid
                  - auid
                to: user.effective
            event:
              action:
                - changed-login-id-to
              category:
                - authentication
              type:
                - start
        MAC_CHECK:
          - event:
              action:
                - mac-permission
        MAC_CONFIG_CHANGE:
          - event:
              action:
                - changed-selinux-boolean
              category:
                - configuration
              type:
                - change
        MAC_POLICY_LOAD:
          - event:
              action:
                - loaded-selinux-policy
              category:
                - configuration
              type:
                - access
        MAC_STATUS:
          - event:
              action:
                - changed-selinux-enforcement
              category:
                - configuration
              type:
                - change
        NETFILTER_CFG:
          - event:
              action:
                - loaded-firewall-rule-to
              category:
                - configuration
              type:
                - change
        ROLE_ASSIGN:
          - event:
              action:
                - assigned-user-role-to
              category:
                - iam
              type:
                - user
                - change
        ROLE_MODIFY:
          - event:
              action:
                - modified-role
              category:
                - iam
              type:
                - change
        ROLE_REMOVE:
          - event:
              action:
                - removed-user-role-from
              category:
                - iam
              type:
                - user
                - change
        SECCOMP:
          - event:
              action:
                - violated-seccomp-policy
        SELINUX_ERR:
          - event:
              action:
                - caused-mac-policy-error
        SERVICE_START:
          - event:
              action:
                - started-service
              category:
                - process
              type:
                - start
        SERVICE_STOP:
          - event:
              action:
                - stopped-service
              category:
                - process
              type:
                - stop
        SOFTWARE_UPDATE:
          - event:
              action:
                - package-updated
              category:
                - package
              type:
                - info
        SYSTEM_BOOT:
          - event:
              action:
                - booted-system
              category:
                - host
              type:
                - start
        SYSTEM_RUNLEVEL:
          - event:
              action:
                - changed-to-runlevel
              category:
                - host
              type:
                - change
        SYSTEM_SHUTDOWN:
          - event:
              action:
                - shutdown-system
              category:
                - host
              type:
                - end
        TEST:
          - event:
              action:
                - sent-test
              category:
                - process
              type:
                - info
        TRUSTED_APP:
          - event:
              action:
                - unknown
              category:
                - process
              type:
                - info
        TTY:
          - event:
              action:
                - typed
        USER:
          - event:
              action:
                - sent-message
        USER_ACCT:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - was-authorized
              category:
                - authentication
              type:
                - info
        USER_AUTH:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - authenticated
              category:
                - authentication
              type:
                - info
        USER_AVC:
          - event:
              action:
                - access-permission
        USER_CHAUTHTOK:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - uid
                to: user.effective
              - from:
                  - acct
                  - id
                  - uid
                to: user.target
            event:
              action:
                - changed-password
              category:
                - iam
              type:
                - user
                - change
        USER_CMD:
          - event:
              action:
                - ran-command
              category:
                - process
              type:
                - start
        USER_END:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - ended-session
              category:
                - session
              type:
                - end
        USER_ERR:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - error
              category:
                - authentication
              type:
                - info
        USER_LOGIN:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - logged-in
              category:
                - authentication
              type:
                - start
        USER_LOGOUT:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - logged-out
              category:
                - authentication
              type:
                - end
        USER_MAC_CONFIG_CHANGE:
          - event:
              action:
                - changed-mac-configuration
              category:
                - configuration
              type:
                - change
        USER_MAC_POLICY_LOAD:
          - event:
              action:
                - loaded-mac-policy
              category:
                - configuration
              type:
                - access
        USER_MGMT:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.target
              - from:
                  - uid
                to: user.effective
            event:
              action:
                - modified-user-account
              category:
                - iam
              type:
                - user
                - change
        USER_ROLE_CHANGE:
          - event:
              action:
                - changed-role-to
        USER_SELINUX_ERR:
          - event:
              action:
                - access-error
        USER_START:
          - copy:
              - from:
                  - auid
                to: user
              - from:
                  - acct
                  - id
                  - uid
                to: user.effective
            event:
              action:
                - started-session
              category:
                - session
              type:
                - start
        USER_TTY:
          - event:
              action:
                - typed
        USYS_CONFIG:
          - event:
              action:
                - changed-configuration
              category:
                - configuration
              type:
                - change
        VIRT_CONTROL:
          - event:
              action:
                - issued-vm-control
              category:
                - host
              type:
                - info
        VIRT_CREATE:
          - event:
              action:
                - created-vm-image
              category:
                - host
              type:
                - info
        VIRT_DESTROY:
          - event:
              action:
                - deleted-vm-image
              category:
                - host
              type:
                - info
        VIRT_INTEGRITY_CHECK:
          - event:
              action:
                - checked-integrity-of
              category:
                - host
              type:
                - info
        VIRT_MACHINE_ID:
          - event:
              action:
                - assigned-vm-id
              category:
                - host
              type:
                - info
        VIRT_MIGRATE_IN:
          - event:
              action:
                - migrated-vm-from
              category:
                - host
              type:
                - info
        VIRT_MIGRATE_OUT:
          - event:
              action:
                - migrated-vm-to
              category:
                - host
              type:
                - info
        VIRT_RESOURCE:
          - event:
              action:
                - assigned-vm-resource
              category:
                - host
              type:
                - info
    # END OF AUTOGENERATED
    source: >-
      boolean hasFields(HashMap base, def list) {
        if (list == null) return true;
        for (int i=0; i<list.length; i++)
          if (base[list[i]] == null) return false;
        return true;
      }
      if (ctx?.auditd?.log?.record_type == null) {
        return;
      }
      HashMap base = ctx.auditd.log;
      def acts = params.types.get(base.record_type);
      if (acts == null && base.syscall != null) {
        acts = params.syscalls.get(base?.syscall);
        if (acts == null) acts = params.syscalls.get('*');
      }
      if (acts == null) return;
      def act = null;
      for (int i=0; act == null && i<acts.length; i++) {
        if (hasFields(base, acts[i]["has_fields"])) act = acts[i];
      }

      if (act?.event != null) {
        def hm = new HashMap(act.event);
        hm.forEach((k, v) -> ctx.event[k] = v);
      }
      if (act?.copy != null) {
        List lst = new ArrayList();
        for(int i=0; i<act.copy.length; i++) {
          def value;
          def srcList = act.copy[i]["from"];
          for (int j=0; value == null && j<srcList.length; j++) {
            value = base[srcList[j]];
          }
          if (value != null && value instanceof String) {
            String suffix = value ==~ /[0-9]+/? ".id" : ".name";
            lst.add([
              "target": act.copy[i]["to"] + suffix,
              "value": value
            ]);
          }
        }
        if (lst.size() > 0) {
          ctx.auditd.log["copy"] = lst;
        }
      }
- foreach:
    field: auditd.log.copy
    ignore_missing: true
    processor:
      set:
        field: "{{_ingest._value.target}}"
        value: "{{_ingest._value.value}}"
- set:
    if: "ctx.auditd.log?.record_type == 'SYSTEM_BOOT' || ctx.auditd.log?.record_type == 'SYSTEM_SHUTDOWN'"
    field: event.category
    value: host
- set:
    if: "ctx.auditd.log?.record_type == 'SYSTEM_BOOT' || ctx.auditd.log?.record_type == 'SYSTEM_SHUTDOWN'"
    field: event.type
    value: info
- set:
    if: "ctx.auditd.log?.record_type == 'SYSCALL' && ctx.auditd.log?.syscall == 'execve'"
    field: event.category
    value: process
- set:
    if: "ctx.auditd.log?.record_type == 'SYSCALL' && ctx.auditd.log?.syscall == 'execve'"
    field: event.type
    value: info
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_CONTROL' || ctx.auditd.log?.record_type == 'VIRT_MACHINE_ID'"
    field: event.category
    value: host
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_CONTROL' && ctx.auditd.log?.op == 'start'"
    field: event.type
    value: start
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_CONTROL' && ctx.auditd.log?.op == 'stop'"
    field: event.type
    value: end
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_CONTROL' && ctx.auditd.log?.op == 'create'"
    field: event.type
    value: creation
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_CONTROL' && ctx.auditd.log?.op == 'delete'"
    field: event.type
    value: deletion
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_MACHINE_ID'"
    field: event.type
    value: creation
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_MACHINE_ID'"
    field: container.name
    value: "{{ auditd.log.vm }}"
    ignore_empty_value: true
- set:
    if: "ctx.auditd.log?.record_type == 'VIRT_MACHINE_ID'"
    field: container.runtime
    value: "{{ auditd.log.virt }}"
    ignore_empty_value: true
- set:
    if: >
      ctx.auditd.log?.record_type == 'SYSCALL' && (
      ctx.auditd.log?.syscall == 'accept' || ctx.auditd.log?.syscall == '43' ||
      ctx.auditd.log?.syscall == 'recvfrom' || ctx.auditd.log?.syscall == '45' ||
      ctx.auditd.log?.syscall == 'recvmsg' || ctx.auditd.log?.syscall == '47' ||
      ctx.auditd.log?.syscall == 'accept4' || ctx.auditd.log?.syscall == '288' )
    field: network.direction
    value: ingress
- set:
    if: >
      ctx.auditd.log?.record_type == 'SYSCALL' && (
      ctx.auditd.log?.syscall == 'connect' || ctx.auditd.log?.syscall == '42' ||
      ctx.auditd.log?.syscall == 'sendto' || ctx.auditd.log?.syscall == '44' ||
      ctx.auditd.log?.syscall == 'sendmsg' || ctx.auditd.log?.syscall == '46')
    field: network.direction
    value: egress
- rename:
    ignore_failure: true
    field: auditd.log.arch
    target_field: host.architecture
- rename:
    ignore_failure: true
    field: auditd.log.acct
    target_field: user.name
- rename:
    ignore_failure: true
    field: auditd.log.user
    target_field: user.name
- rename:
    ignore_failure: true
    field: auditd.log.uid
    target_field: user.id
- rename:
    ignore_failure: true
    field: auditd.log.gid
    target_field: user.group.id
- rename:
    ignore_failure: true
    field: auditd.log.agid
    target_field: user.audit.group.id
- rename:
    ignore_failure: true
    field: auditd.log.auid
    target_field: user.audit.id
- rename:
    ignore_failure: true
    field: auditd.log.fsgid
    target_field: user.filesystem.group.id
- rename:
    ignore_failure: true
    field: auditd.log.fsuid
    target_field: user.filesystem.id
- rename:
    ignore_failure: true
    field: auditd.log.egid
    target_field: user.effective.group.id
- rename:
    ignore_failure: true
    field: auditd.log.euid
    target_field: user.effective.id
- rename:
    ignore_failure: true
    field: auditd.log.sgid
    target_field: user.saved.group.id
- rename:
    ignore_failure: true
    field: auditd.log.suid
    target_field: user.saved.id
- rename:
    ignore_failure: true
    field: auditd.log.ogid
    target_field: user.owner.group.id
- rename:
    ignore_failure: true
    field: auditd.log.ouid
    target_field: user.owner.id
- rename:
    ignore_failure: true
    field: auditd.log.comm
    target_field: process.name
- rename:
    ignore_failure: true
    field: auditd.log.exe
    target_field: process.executable
- rename:
    ignore_failure: true
    field: auditd.log.pid
    target_field: process.pid
- rename:
    ignore_failure: true
    field: auditd.log.ppid
    target_field: process.parent.pid
- convert:
    ignore_missing: true
    field: process.pid
    type: long
- convert:
    ignore_missing: true
    field: process.parent.pid
    type: long
- rename:
    ignore_failure: true
    field: auditd.log.cmd
    target_field: process.args
- split:
    ignore_failure: true
    field: process.args
    separator: "\\s+"
- rename:
    ignore_failure: true
    field: auditd.log.argc
    target_field: process.args_count
- script:
    if: "ctx?.process?.args != null"
    lang: painless
    source: >-
      if (ctx.process.args instanceof List) {
        ctx.process.args_count = ctx.process.args.length;
      }
- convert:
    ignore_missing: true
    field: process.args_count
    type: long
- rename:
    ignore_failure: true
    field: auditd.log.exit
    target_field: process.exit_code
- convert:
    ignore_missing: true
    field: process.exit_code
    type: long
- rename:
    ignore_missing: true
    field: auditd.log.cwd
    target_field: process.working_directory
- rename:
    ignore_failure: true
    field: auditd.log.terminal
    target_field: user.terminal
- rename:
    ignore_failure: true
    field: auditd.log.msg
    target_field: message

# The processor below populates process.args list from argN fields.
#
# It handles the common case of a complete record: Contains argc=N and a0 to aN-1,
# and the truncated case: Contains aI, aI+1, ..., aN-1, for I>0, and no argc.
- script:
    lang: painless
    description: Extracts process arguments from EXECVE calls.
    if: 'ctx.auditd?.log?.record_type == "EXECVE"'
    source: >-
      /* Want to capture all aNN fields, including aN_len and aN[x] */
      Pattern argRegex = /^a([0-9]+)(.*)$/;

      List keys = ctx.auditd.log.keySet().stream()
                  /* From List of keys to list of matchers */
                  .map(x -> argRegex.matcher(x))
                  /* Drop elements that didn't match the regex */
                  .filter(x -> x.matches())
                  /* Must save to a list because it needs to remove keys in auditd.log,
                     which cannot be done while streaming from this source */
                  .collect(Collectors.toList());

      List args = keys.stream()
                  /* List<Matcher> to List<[Matcher, Value for given key]>
                     with side effect of removing the key */
                  .map(x -> [x, ctx.auditd.log.remove(x.group(0))])
                  /* Drop elements that end in _len, just wanted to remove them */
                  .filter(x -> x[0].group(2) != "_len")
                  /* List<Matcher, Value> to List<[Int, Value]>
                     where the Int is the argument index */
                  .map(x -> [Integer.parseInt(x[0].group(1)), x[1]])
                  /* Sort by numeric argument index */
                  .sorted((lhs, rhs) -> lhs[0].compareTo(rhs[0]))
                  /* Save as List<[Index, Value]> */
                  .collect(Collectors.toList());

      if (args.isEmpty()) return;
      if (ctx.process == null) ctx.process = new HashMap();
      ctx.process.args = args.stream().map(x -> x[1]).collect(Collectors.toList());
      def firstIndex = args[0][0];
      if (firstIndex == 0) {
        ctx.process.executable = ctx.process.args[0];
      } else {
        ctx.process.args.add(0, "[... " + firstIndex + " truncated arguments ...]");
      }

    on_failure:
      - append:
          field: error.message
          value: "failed extracting process arguments: {{{ _ingest.on_failure_message }}}"

- rename:
    ignore_failure: true
    field: auditd.log.res
    target_field: event.outcome
- rename:
    ignore_failure: true
    field: auditd.log.record_type
    target_field: event.action
- lowercase:
    ignore_failure: true
    field: event.action
- rename:
    ignore_failure: true
    field: auditd.log.src
    target_field: source.address
- rename:
    ignore_failure: true
    field: auditd.log.addr
    target_field: source.address
    if: ctx?.source?.address == null
- rename:
    ignore_failure: true
    field: auditd.log.dst
    target_field: destination.address
- grok:
    field: source.address
    patterns:
    - "^%{IP:source.ip}$"
    ignore_failure: true
- geoip:
    field: source.ip
    target_field: source.geo
    ignore_failure: true
- geoip:
    database_file: GeoLite2-ASN.mmdb
    field: source.ip
    target_field: source.as
    properties:
    - asn
    - organization_name
    ignore_missing: true
- rename:
    field: source.as.asn
    target_field: source.as.number
    ignore_missing: true
- rename:
    field: source.as.organization_name
    target_field: source.as.organization.name
    ignore_missing: true
- remove:
    field:
      - auditd.log.kv
      - auditd.log.sub_kv
      - auditd.log.epoch
      - auditd.log.copy
    ignore_failure: true
    ignore_missing: true
on_failure:
- append:
    field: error.message
    value: "{{ _ingest.on_failure_message }}"
