type: aws-s3
{{ if .queue_url }}
queue_url: {{ .queue_url }}
{{ end }}
{{ if .bucket_arn }}
bucket_arn: {{ .bucket_arn }}
{{ end }}

{{ if .number_of_workers }}
number_of_workers: {{ .number_of_workers }}
{{ end }}

{{ if .bucket_list_interval }}
bucket_list_interval: {{ .bucket_list_interval }}
{{ end }}

{{ if .bucket_list_prefix }}
bucket_list_prefix: {{ .bucket_list_prefix }}
{{ end }}

{{ if .credential_profile_name }}
credential_profile_name: {{ .credential_profile_name }}
{{ end }}

{{ if .shared_credential_file }}
shared_credential_file: {{ .shared_credential_file }}
{{ end }}

{{ if .visibility_timeout }}
visibility_timeout: {{ .visibility_timeout }}
{{ end }}

{{ if .api_timeout }}
api_timeout: {{ .api_timeout }}
{{ end }}

{{ if .endpoint }}
endpoint: {{ .endpoint }}
{{ end }}

{{ if .default_region }}
default_region: {{ .default_region }}
{{ end }}

{{ if .access_key_id }}
access_key_id: {{ .access_key_id }}
{{ end }}

{{ if .secret_access_key }}
secret_access_key: {{ .secret_access_key }}
{{ end }}

{{ if .session_token }}
session_token: {{ .session_token }}
{{ end }}

{{ if .role_arn }}
role_arn: {{ .role_arn }}
{{ end }}

{{ if .fips_enabled }}
fips_enabled: {{ .fips_enabled }}
{{ end }}

{{ if .max_number_of_messages }}
max_number_of_messages: {{ .max_number_of_messages }}
{{ end }}

{{ if .proxy_url }}
proxy_url: {{ .proxy_url }}
{{ end }}

{{ if .ssl }}
ssl: {{ .ssl | tojson }}
{{ end }}

tags: {{.tags | tojson}}
publisher_pipeline.disable_host: {{ inList .tags "forwarded" }}

processors:
  - add_fields:
      target: ''
      fields:
        ecs.version: 1.12.0
