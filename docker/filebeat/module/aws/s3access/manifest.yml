module_version: 1.0

var:
  - name: input
    default: aws-s3
  - name: queue_url
  - name: bucket_arn
  - name: number_of_workers
  - name: bucket_list_interval
  - name: bucket_list_prefix
  - name: shared_credential_file
  - name: credential_profile_name
  - name: visibility_timeout
  - name: api_timeout
  - name: endpoint
  - name: default_region
  - name: access_key_id
  - name: secret_access_key
  - name: session_token
  - name: role_arn
  - name: tags
    default: [forwarded]
  - name: fips_enabled
  - name: proxy_url
  - name: max_number_of_messages
  - name: ssl

ingest_pipeline: ingest/pipeline.yml
input: config/{{.input}}.yml
