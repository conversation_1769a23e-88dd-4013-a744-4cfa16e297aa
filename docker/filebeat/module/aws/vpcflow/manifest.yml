module_version: 1.0

var:
  - name: input
    default: aws-s3
  - name: queue_url
  - name: bucket_arn
  - name: number_of_workers
  - name: bucket_list_interval
  - name: bucket_list_prefix
  - name: shared_credential_file
  - name: credential_profile_name
  - name: visibility_timeout
  - name: api_timeout
  - name: endpoint
  - name: default_region
  - name: access_key_id
  - name: secret_access_key
  - name: session_token
  - name: role_arn
  - name: tags
    default: [forwarded, preserve_original_event]
  - name: fips_enabled
  - name: proxy_url
  - name: max_number_of_messages
  - name: ssl
  - name: format
    default:
      - version account-id interface-id srcaddr dstaddr srcport dstport protocol packets bytes start end action log-status
      - instance-id interface-id srcaddr dstaddr pkt-srcaddr pkt-dstaddr
      - version interface-id account-id vpc-id subnet-id instance-id srcaddr dstaddr srcport dstport protocol tcp-flags type pkt-srcaddr pkt-dstaddr action log-status
      - version vpc-id subnet-id instance-id interface-id account-id type srcaddr dstaddr srcport dstport pkt-srcaddr pkt-dstaddr protocol bytes packets start end action tcp-flags log-status

ingest_pipeline: ingest/pipeline.yml
input: config/input.yml
