{"attributes": {"columns": ["_source"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.module", "negate": false, "params": {"query": "threatintel"}, "type": "phrase"}, "query": {"match_phrase": {"event.module": "threatintel"}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "event.category", "negate": false, "params": {"query": "threat"}, "type": "phrase"}, "query": {"match_phrase": {"event.category": "threat"}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[2].meta.index", "key": "event.kind", "negate": false, "params": {"query": "enrichment"}, "type": "phrase"}, "query": {"match_phrase": {"event.kind": "enrichment"}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[3].meta.index", "key": "event.type", "negate": false, "params": {"query": "indicator"}, "type": "phrase"}, "query": {"match_phrase": {"event.type": "indicator"}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [], "title": "All Logs [Filebeat Threat Intel] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "6acbb070-72d0-11eb-a3e3-b3cc7c78a70f", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[2].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[3].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:32.145Z", "version": "WzQ2NjAsMV0="}