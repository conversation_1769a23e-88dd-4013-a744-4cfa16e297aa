{"attributes": {"columns": ["osquery.result.name", "osquery.result.columns.name", "osquery.result.columns.status"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "osquery.result.name", "negate": false, "params": {"query": "pack_it-compliance_kernel_modules", "type": "phrase"}, "type": "phrase", "value": "pack_it-compliance_kernel_modules"}, "query": {"match": {"osquery.result.name": {"query": "pack_it-compliance_kernel_modules", "type": "phrase"}}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "Kernel modules [Filebeat Osquery] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "f59e21e0-eb03-11e7-8f04-51231daa5b05-ecs", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:54.660Z", "version": "WzQ5MzEsMV0="}