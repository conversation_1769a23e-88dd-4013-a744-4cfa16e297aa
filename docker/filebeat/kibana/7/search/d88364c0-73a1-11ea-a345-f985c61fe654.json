{"attributes": {"columns": ["user.email", "service.name", "gcp.audit.type", "event.action", "event.outcome", "source.ip", "source.geo.region_name"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "gcp.audit"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "gcp.audit"}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [], "title": "Audit [Filebeat GCP]", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "d88364c0-73a1-11ea-a345-f985c61fe654", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:41.323Z", "version": "WzQ3ODUsMV0="}