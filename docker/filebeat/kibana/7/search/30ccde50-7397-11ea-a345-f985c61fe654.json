{"attributes": {"columns": ["user.id", "event.provider", "aws.cloudtrail.event_type", "event.action", "event.outcome", "source.address", "source.geo.region_name"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "aws.cloudtrail"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "aws.cloudtrail"}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [], "title": "CloudTrail Events [Filebeat AWS]", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "30ccde50-7397-11ea-a345-f985c61fe654", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:35.213Z", "version": "WzQ2OTUsMV0="}