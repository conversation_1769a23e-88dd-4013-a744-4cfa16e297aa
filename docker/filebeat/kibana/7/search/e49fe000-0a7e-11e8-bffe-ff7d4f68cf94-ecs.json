{"attributes": {"columns": ["mongodb.log.timestamp", "log.level", "mongodb.log.component", "mongodb.log.context", "message"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "log.level: F or log.level: W"}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "Error logs [Filebeat MongoDB] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "e49fe000-0a7e-11e8-bffe-ff7d4f68cf94-ecs", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:33:48.189Z", "version": "WzQzMDEsMV0="}