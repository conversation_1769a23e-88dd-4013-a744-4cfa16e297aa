{"attributes": {"columns": ["_source"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.module", "negate": false, "params": {"query": "mysql", "type": "phrase"}, "type": "phrase", "value": "mysql"}, "query": {"match": {"event.module": {"query": "mysql", "type": "phrase"}}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "fileset.name", "negate": false, "params": {"query": "slowlog", "type": "phrase"}, "type": "phrase", "value": "slowlog"}, "query": {"match": {"fileset.name": {"query": "slowlog", "type": "phrase"}}}}], "highlight": {"fields": {"*": {}}, "fragment_size": 2147483647, "post_tags": ["@/kibana-highlighted-field@"], "pre_tags": ["@kibana-highlighted-field@"], "require_field_match": false}, "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "Slow logs [Filebeat MySQL] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "Filebeat-MySQL-Slow-log-ecs", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:13.657Z", "version": "WzQ0NjAsMV0="}