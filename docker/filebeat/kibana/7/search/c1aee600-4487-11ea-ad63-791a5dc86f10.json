{"attributes": {"columns": ["source.ip", "source.port", "event.original"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "fileset.name", "negate": false, "params": {"query": "vpcflow"}, "type": "phrase", "value": "vpcflow"}, "query": {"match": {"fileset.name": {"query": "vpcflow", "type": "phrase"}}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "aws.vpcflow.action", "negate": false, "params": {"query": "REJECT"}, "type": "phrase", "value": "REJECT"}, "query": {"match": {"aws.vpcflow.action": {"query": "REJECT", "type": "phrase"}}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "VPC Flow Reject Logs [Filebeat AWS]", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "c1aee600-4487-11ea-ad63-791a5dc86f10", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:00.470Z", "version": "WzQzNzgsMV0="}