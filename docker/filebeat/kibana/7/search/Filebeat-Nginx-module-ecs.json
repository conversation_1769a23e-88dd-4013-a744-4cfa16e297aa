{"attributes": {"columns": ["url.original", "http.request.method", "http.response.status_code", "http.request.referrer", "http.response.body.bytes"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "highlight": {"fields": {"*": {}}, "fragment_size": 2147483647, "post_tags": ["@/kibana-highlighted-field@"], "pre_tags": ["@kibana-highlighted-field@"], "require_field_match": false}, "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.module:nginx"}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "Nginx logs [Filebeat Nginx] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "Filebeat-Nginx-module-ecs", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:34:19.828Z", "version": "WzQ1MTIsMV0="}