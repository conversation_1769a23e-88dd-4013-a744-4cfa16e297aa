{"attributes": {"columns": ["_source"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "okta.system"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "okta.system"}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "event.outcome", "negate": false, "params": {"query": "FAILURE"}, "type": "phrase"}, "query": {"match_phrase": {"event.outcome": "FAILURE"}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [["@timestamp", "desc"], ["event.created", "desc"]], "title": "Okta Failure Events", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "21028750-67ca-11ea-a76f-bf44814e437d", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:33:39.099Z", "version": "WzQyMzksMV0="}