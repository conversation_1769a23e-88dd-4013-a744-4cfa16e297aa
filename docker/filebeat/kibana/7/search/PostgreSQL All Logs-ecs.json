{"attributes": {"columns": ["user.name", "postgresql.log.database", "log.level", "message", "postgresql.log.query"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "postgresql.log"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "postgresql.log"}}}], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "All Logs [Filebeat PostgreSQL] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "PostgreSQL All Logs-ecs", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:33:50.312Z", "version": "WzQzMTQsMV0="}