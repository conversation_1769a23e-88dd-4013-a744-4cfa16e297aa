{"attributes": {"columns": ["user.name", "postgresql.log.database", "event.duration", "postgresql.log.query"], "description": "", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset:\"postgresql.log\" AND event.duration>30000000"}, "version": true}}, "sort": [["@timestamp", "desc"]], "title": "Slow Queries [Filebeat PostgreSQL] ECS", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "Slow PostgreSQL Queries-ecs", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "search", "updated_at": "2021-08-04T16:33:51.290Z", "version": "WzQzMTgsMV0="}