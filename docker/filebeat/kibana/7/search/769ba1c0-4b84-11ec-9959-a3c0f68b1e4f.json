{"attributes": {"columns": ["salesforce.setup_audit_trail.section", "event.action", "salesforce.setup_audit_trail.display"], "description": "", "grid": {}, "hideChart": false, "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "salesforce.setupaudittrail"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "salesforce.setupaudittrail"}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "sort": [["@timestamp", "desc"]], "title": "SetupAuditTrail Log Stream [Filebeat Salesforce]", "version": 1}, "coreMigrationVersion": "7.15.0", "id": "769ba1c0-4b84-11ec-9959-a3c0f68b1e4f", "migrationVersion": {"search": "7.9.3"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "search", "updated_at": "2022-05-06T11:14:57.093Z", "version": "WzMzNDg4LDNd"}