{"attributes": {"description": "Autonomous systems Netflow", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "globalState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "input.type", "negate": false, "params": {"query": "netflow"}, "type": "phrase", "value": "netflow"}, "query": {"match": {"input.type": {"query": "netflow", "type": "phrase"}}}}], "highlightAll": true, "query": {"language": "kuery", "query": ""}, "version": true}}, "optionsJSON": {"darkTheme": false}, "panelsJSON": [{"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 4, "i": "1", "w": 48, "x": 0, "y": 0}, "panelIndex": "1", "panelRefName": "panel_1", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "2", "w": 24, "x": 24, "y": 12}, "panelIndex": "2", "panelRefName": "panel_2", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "3", "w": 24, "x": 24, "y": 20}, "panelIndex": "3", "panelRefName": "panel_3", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "4", "w": 24, "x": 0, "y": 12}, "panelIndex": "4", "panelRefName": "panel_4", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "5", "w": 24, "x": 0, "y": 20}, "panelIndex": "5", "panelRefName": "panel_5", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "6", "w": 16, "x": 0, "y": 4}, "panelIndex": "6", "panelRefName": "panel_6", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "7", "w": 16, "x": 16, "y": 4}, "panelIndex": "7", "panelRefName": "panel_7", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "8", "w": 16, "x": 32, "y": 4}, "panelIndex": "8", "panelRefName": "panel_8", "type": "visualization", "version": "7.3.0"}], "timeRestore": false, "title": "[Filebeat Netflow] Autonomous Systems", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "c64665f9-d222-421e-90b0-c7310d944b8a", "migrationVersion": {"dashboard": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "d4e6520a-9ced-47c9-a8f2-7246e8cbd2d3", "name": "1:panel_1", "type": "visualization"}, {"id": "12aad647-c45d-4667-a029-152c1a97cbbc", "name": "2:panel_2", "type": "visualization"}, {"id": "d27b5d74-b3b4-4311-a0e6-08ff8f4345df", "name": "3:panel_3", "type": "visualization"}, {"id": "751ecb6f-11c3-458d-b039-f6d57a6379fa", "name": "4:panel_4", "type": "visualization"}, {"id": "f75063c7-48b7-4de4-b8cb-d07eb2cea0e9", "name": "5:panel_5", "type": "visualization"}, {"id": "f7808e70-df2a-4532-a350-966704567c24", "name": "6:panel_6", "type": "visualization"}, {"id": "aed09724-0a69-4331-84f5-3d2067c43930", "name": "7:panel_7", "type": "visualization"}, {"id": "f531f957-e8c0-497a-ad41-ef39c2d29671", "name": "8:panel_8", "type": "visualization"}], "type": "dashboard", "updated_at": "2021-08-04T16:34:42.347Z", "version": "WzQ3ODYsMV0="}