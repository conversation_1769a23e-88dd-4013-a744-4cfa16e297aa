{"attributes": {"description": "Netflow exporters", "hits": 0, "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "globalState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "input.type", "negate": false, "params": {"query": "netflow"}, "type": "phrase", "value": "netflow"}, "query": {"match": {"input.type": {"query": "netflow", "type": "phrase"}}}}], "highlightAll": true, "query": {"language": "kuery", "query": ""}, "version": true}}, "optionsJSON": {"darkTheme": false}, "panelsJSON": [{"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 4, "i": "1", "w": 48, "x": 0, "y": 0}, "panelIndex": "1", "panelRefName": "panel_1", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "2", "w": 16, "x": 0, "y": 4}, "panelIndex": "2", "panelRefName": "panel_2", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "3", "w": 16, "x": 16, "y": 4}, "panelIndex": "3", "panelRefName": "panel_3", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "4", "w": 16, "x": 32, "y": 4}, "panelIndex": "4", "panelRefName": "panel_4", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "5", "w": 24, "x": 24, "y": 12}, "panelIndex": "5", "panelRefName": "panel_5", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "6", "w": 24, "x": 24, "y": 20}, "panelIndex": "6", "panelRefName": "panel_6", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "8", "w": 24, "x": 0, "y": 20}, "panelIndex": "8", "panelRefName": "panel_8", "type": "visualization", "version": "7.3.0"}, {"embeddableConfig": {"enhancements": {}}, "gridData": {"h": 8, "i": "10", "w": 24, "x": 0, "y": 12}, "panelIndex": "10", "panelRefName": "panel_10", "type": "visualization", "version": "7.3.0"}], "timeRestore": false, "title": "[Filebeat Netflow] Flow Exporters", "version": 1}, "coreMigrationVersion": "8.0.0", "id": "feebb4e6-b13e-4e4e-b9fc-d3a178276425", "migrationVersion": {"dashboard": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "d4e6520a-9ced-47c9-a8f2-7246e8cbd2d3", "name": "1:panel_1", "type": "visualization"}, {"id": "441c6c50-fa1a-489c-96c6-76f7925dea24", "name": "2:panel_2", "type": "visualization"}, {"id": "14c7136d-b4aa-4367-9461-52bf8b5c4796", "name": "3:panel_3", "type": "visualization"}, {"id": "4ac97841-c89f-4d50-b3c6-6253f7e1dd1a", "name": "4:panel_4", "type": "visualization"}, {"id": "85ebf558-402b-45d2-a186-e15f8673ec07", "name": "5:panel_5", "type": "visualization"}, {"id": "f86a7769-8ef6-408d-bbe3-985d0ea0a3f7", "name": "6:panel_6", "type": "visualization"}, {"id": "1cd36f5d-d9c7-4098-acdb-14d312ecfb72", "name": "8:panel_8", "type": "visualization"}, {"id": "d3df8d28-65f8-4ea1-8b33-f479380a0600", "name": "10:panel_10", "type": "visualization"}], "type": "dashboard", "updated_at": "2021-08-04T16:34:44.454Z", "version": "WzQ4MDEsMV0="}