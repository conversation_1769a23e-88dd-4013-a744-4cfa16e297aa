{"attributes": {"description": "Anomali indicator classification ingested by the threat intel Filebeat module.", "state": {"datasourceStates": {"indexpattern": {"layers": {"41f41086-8875-4d18-8844-b51b9c9cb8bc": {"columnOrder": ["8d7cc68f-5178-40f1-b041-bdb02dea3324", "9afb1b09-0f20-488c-9242-a94f7d11800b"], "columns": {"8d7cc68f-5178-40f1-b041-bdb02dea3324": {"customLabel": true, "dataType": "string", "isBucketed": true, "label": "Anomali Indicator Classification", "operationType": "terms", "params": {"missingBucket": false, "orderBy": {"columnId": "9afb1b09-0f20-488c-9242-a94f7d11800b", "type": "column"}, "orderDirection": "desc", "otherBucket": true, "size": 5}, "scale": "ordinal", "sourceField": "anomali.threatstream.classification"}, "9afb1b09-0f20-488c-9242-a94f7d11800b": {"dataType": "number", "isBucketed": false, "label": "Count of records", "operationType": "count", "scale": "ratio", "sourceField": "Records"}}, "incompleteColumns": {}}}}}, "filters": [], "query": {"language": "kuery", "query": "event.dataset:(threatintel.anomalithreatstream or threatintel.anomali)"}, "visualization": {"layers": [{"categoryDisplay": "default", "groups": ["8d7cc68f-5178-40f1-b041-bdb02dea3324"], "layerId": "41f41086-8875-4d18-8844-b51b9c9cb8bc", "legendDisplay": "default", "metric": "9afb1b09-0f20-488c-9242-a94f7d11800b", "nestedLegend": false, "numberDisplay": "percent"}], "shape": "donut"}}, "title": "Anomali Indicator Classification [Filebeat Threat Intel]", "visualizationType": "lnsPie"}, "coreMigrationVersion": "8.0.0", "id": "36f61650-de96-11eb-8f2b-753caedf727d", "migrationVersion": {"lens": "7.13.1"}, "references": [{"id": "filebeat-*", "name": "indexpattern-datasource-current-indexpattern", "type": "index-pattern"}, {"id": "filebeat-*", "name": "indexpattern-datasource-layer-41f41086-8875-4d18-8844-b51b9c9cb8bc", "type": "index-pattern"}, {"id": "d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "name": "tag-ref-d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "type": "tag"}], "type": "lens", "updated_at": "2021-08-04T16:34:28.102Z", "version": "WzQ1ODUsMV0="}