{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Locality (bytes) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "2", "params": {"customLabel": "Locality", "field": "flow.locality", "order": "desc", "orderBy": "1", "size": 5}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}}, "title": "Locality (bytes) [Filebeat Netflow]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "b02c2713-17f0-41dd-88a3-ce33b446f19d", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:47.530Z", "version": "WzQ4MzAsMV0="}