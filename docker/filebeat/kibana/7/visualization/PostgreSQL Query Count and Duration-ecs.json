{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "postgresql.log"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "postgresql.log"}}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Query count and cumulated duration [Filebeat PostgreSQL] ECS", "uiStateJSON": {"vis": {"colors": {"Number of queries": "#0A437C", "Sum of query duration": "#6ED0E0"}, "legendOpen": true}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "3", "params": {"drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1, "scaleMetricValues": false, "timeRange": {"from": "now-15m", "to": "now"}, "useNormalizedEsInterval": true}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "4", "params": {"customLabel": "Number of queries"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Sum of query duration", "field": "event.duration"}, "schema": "metric", "type": "sum"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {"text": "@timestamp per 3 hours"}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false, "style": {"color": "#eee"}}, "isVislibVis": true, "labels": {"show": false}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "4", "label": "Number of queries"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "normal", "show": true, "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}, {"data": {"id": "2", "label": "Sum of query duration"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "normal", "show": true, "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": ""}, "type": "value"}]}, "title": "Query count and cumulated duration [Filebeat PostgreSQL] ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "PostgreSQL Query Count and Duration-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "PostgreSQL Query Durations-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:33:51.290Z", "version": "WzQzMTcsMV0="}