{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset:redis.log"}, "version": true}}, "title": "Logs over time [Filebeat Redis] ECS", "uiStateJSON": {"vis": {"colors": {"notice": "#629E51", "warning": "#EF843C"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "log.level", "order": "desc", "orderBy": "1", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {"text": "@timestamp per month"}, "type": "category"}], "defaultYExtents": false, "detailedTooltip": true, "drawLinesBetweenPoints": true, "grid": {"categoryLines": false, "style": {"color": "#eee"}}, "interpolate": "linear", "isVislibVis": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "radiusRatio": 9, "scale": "linear", "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "mode": "stacked", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "setYExtents": false, "showCircles": true, "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Logs over time [Filebeat Redis] ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "d2864600-478f-11e7-be88-2ddb32f3df97-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:22.899Z", "version": "WzQ1MzIsMV0="}