{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "salesforce.apex"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "salesforce.apex"}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Categorization by User type [Filebeat Salesforce]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "user.roles", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 20}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "colorSchema": "Greens", "colorsNumber": 4, "colorsRange": [], "enableHover": false, "invertColors": false, "legendPosition": "right", "percentageMode": false, "setColorRange": false, "times": [], "type": "heatmap", "valueAxes": [{"id": "ValueAxis-1", "labels": {"color": "black", "overwriteColor": false, "rotate": 0, "show": false}, "scale": {"defaultYExtents": false, "type": "linear"}, "show": false, "type": "value"}]}, "title": "Categorization by User type [Filebeat Salesforce]", "type": "heatmap"}}, "coreMigrationVersion": "7.15.0", "id": "416da3a0-574f-11ec-8f0b-05e8b06e1b10", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2022-05-06T11:14:55.111Z", "version": "WzMzNDcyLDNd"}