{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": []}}, "savedSearchRefName": "search_0", "title": "Log levels over time [Filebeat Kafka] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"customLabel": "Log Level", "field": "log.level", "order": "desc", "orderBy": "1", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {"text": "@timestamp per day"}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false, "style": {"color": "#eee"}}, "isVislibVis": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "mode": "stacked", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Log levels over time [Filebeat Kafka] ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "3f7c33c0-87ee-11e7-ad9c-db80de0bf8d3-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "All Kafka logs-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:33:46.165Z", "version": "WzQyOTQsMV0="}