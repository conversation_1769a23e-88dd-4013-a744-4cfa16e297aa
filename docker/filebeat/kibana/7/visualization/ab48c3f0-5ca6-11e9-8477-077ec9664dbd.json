{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "envoyproxy.log"}, "type": "phrase", "value": "envoyproxy.log"}, "query": {"match": {"event.dataset": {"query": "envoyproxy.log", "type": "phrase"}}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset:envoyproxy.log"}}}, "title": "Proxy Request Distribution [Filebeat Envoyproxy]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "envoyproxy.proxy_type", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "dimensions": {"metric": {"accessor": 0, "aggType": "count", "format": {"id": "number"}, "params": {}}}, "distinctColors": true, "isDonut": true, "labels": {"last_level": true, "show": false, "truncate": 100, "values": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Proxy Request Distribution [Filebeat Envoyproxy] ", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "ab48c3f0-5ca6-11e9-8477-077ec9664dbd", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:33:42.085Z", "version": "WzQyNjIsMV0="}