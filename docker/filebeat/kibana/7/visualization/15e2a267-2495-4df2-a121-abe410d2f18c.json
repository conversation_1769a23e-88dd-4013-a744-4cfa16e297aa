{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"query_string": {"query": "*"}}}}, "title": "VLANs (packets) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "listeners": {}, "params": {"expression": ".es(index=\"filebeat-*\", metric=\"sum:network.packets\", split=\"netflow.vlan_id:10\", kibana=true).scale_interval(1s).fit(mode=scale).if(operator=\"lt\", if=0, then=0).trim(start=2,end=1).label(regex=\"^.* netflow.vlan_id:(.+) > .*$\", label=\"$1\").lines(width=1, stack=true, fill=1).yaxis(label=\"packets / sec\", min=0)", "interval": "auto"}, "title": "VLANs (packets) [Filebeat Netflow]", "type": "timelion"}}, "coreMigrationVersion": "8.0.0", "id": "15e2a267-2495-4df2-a121-abe410d2f18c", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:49.565Z", "version": "WzQ4ODAsMV0="}