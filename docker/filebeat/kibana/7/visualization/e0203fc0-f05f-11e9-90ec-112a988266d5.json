{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {}}, "title": "User Activity Overview [Filebeat Azure]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"axis_formatter": "number", "axis_position": "left", "axis_scale": "normal", "filter": {"language": "kuery", "query": "event.dataset :\"azure.activitylogs\" and azure.activitylogs.event_category :\"Administrative\" and azure.activitylogs.identity.claims_initiated_by_user.fullname :*"}, "id": "61ca57f0-469d-11e7-af02-69e470af7417", "index_pattern": "filebeat-*", "interval": "auto", "isModelInvalid": false, "series": [{"axis_position": "right", "chart_type": "bar", "color": "rgba(1,155,143,1)", "fill": "0.4", "filter": "", "formatter": "number", "hide_in_legend": 0, "id": "61ca57f1-469d-11e7-af02-69e470af7417", "label": "Actions", "line_width": 1, "metrics": [{"id": "61ca57f2-469d-11e7-af02-69e470af7417", "type": "count"}], "point_size": 1, "seperate_axis": 0, "split_color_mode": "gradient", "split_filters": [{"color": "rgba(244,78,59,1)", "filter": {"language": "lucene", "query": "_exists_:identity.claims.name"}, "id": "a5302500-1399-11e8-a699-f390e75f4dd5", "label": ""}], "split_mode": "everything", "stacked": "none"}], "show_grid": 1, "show_legend": 0, "time_field": null, "type": "timeseries", "use_kibana_indexes": false}, "title": "User Activity Overview [Filebeat Azure]", "type": "metrics"}}, "coreMigrationVersion": "8.0.0", "id": "e0203fc0-f05f-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:03.514Z", "version": "WzQ0MDIsMV0="}