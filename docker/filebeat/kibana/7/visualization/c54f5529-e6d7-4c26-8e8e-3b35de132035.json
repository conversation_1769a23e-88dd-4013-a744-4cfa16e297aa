{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Destination and Source Ports (bytes) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "2", "params": {"customLabel": "Destination Port", "field": "destination.port", "order": "desc", "orderBy": "1", "size": 50}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Source Port", "field": "source.port", "order": "desc", "orderBy": "1", "size": 50}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}}, "title": "Destination and Source Ports (bytes) [Filebeat Netflow]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "c54f5529-e6d7-4c26-8e8e-3b35de132035", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:43.370Z", "version": "WzQ3OTksMV0="}