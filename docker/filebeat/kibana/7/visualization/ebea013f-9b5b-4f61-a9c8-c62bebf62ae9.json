{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Conversation Partners [Filebeat Netflow]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": 2, "direction": "desc"}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "2", "params": {"customLabel": "Packets", "field": "network.packets"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "3", "params": {"customLabel": "Source", "field": "source.ip", "order": "desc", "orderBy": "1", "size": 50}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "4", "params": {"customLabel": "Destination", "field": "destination.ip", "order": "desc", "orderBy": "1", "size": 50}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "5", "params": {"customLabel": "Flow Records"}, "schema": "metric", "type": "count"}], "listeners": {}, "params": {"perPage": 10, "showMeticsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": 2, "direction": "desc"}, "totalFunc": "sum"}, "title": "Conversation Partners [Filebeat Netflow]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "ebea013f-9b5b-4f61-a9c8-c62bebf62ae9", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:43.370Z", "version": "WzQ3OTYsMV0="}