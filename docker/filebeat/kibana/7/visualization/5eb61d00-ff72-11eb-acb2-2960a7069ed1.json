{"attributes": {"description": "This is a datatable for the most recently created indicators", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "ThreatQ Recently Created Indicators Datatable [Filebeat Threat Intel]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Last Modified", "field": "@timestamp"}, "schema": "metric", "type": "max"}, {"enabled": true, "id": "2", "params": {"customLabel": "Indicator Type", "field": "threat.indicator.type", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 100}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Indicator Value", "field": "threatq.indicator_value", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 1}, "schema": "bucket", "type": "terms"}], "params": {"perPage": 10, "percentageCol": "", "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": false, "showTotal": false, "totalFunc": "sum"}, "title": "ThreatQ Recently Created Indicators Datatable [Filebeat Threat Intel]", "type": "table"}}, "coreMigrationVersion": "7.15.0", "id": "5eb61d00-ff72-11eb-acb2-2960a7069ed1", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "name": "tag-d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "type": "tag"}], "type": "visualization", "updated_at": "2021-10-11T08:07:14.354Z", "version": "WzYxNCwxXQ=="}