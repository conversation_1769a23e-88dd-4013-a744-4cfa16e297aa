{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {}}, "title": "ELB Inbound Traffic [Filebeat AWS] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"axis_formatter": "number", "axis_min": "0", "axis_position": "left", "axis_scale": "normal", "id": "61ca57f0-469d-11e7-af02-69e470af7417", "index_pattern": "filebeat-*", "interval": "", "isModelInvalid": false, "legend_position": "bottom", "series": [{"axis_position": "right", "chart_type": "line", "color": "rgba(104,204,202,1)", "fill": 0.5, "filter": {"language": "kuery", "query": "fileset.name : \"elb\""}, "formatter": "bytes", "id": "61ca57f1-469d-11e7-af02-69e470af7417", "label": "Inbound", "line_width": 1, "metrics": [{"field": "source.bytes", "id": "61ca57f2-469d-11e7-af02-69e470af7417", "type": "sum"}], "point_size": 1, "separate_axis": 0, "split_color_mode": "gradient", "split_mode": "terms", "stacked": "none", "terms_field": "aws.elb.name", "type": "timeseries"}], "show_grid": 1, "show_legend": 1, "time_field": "", "type": "timeseries", "use_kibana_indexes": false}, "title": "ELB Inbound Traffic [Filebeat AWS] ECS", "type": "metrics"}}, "coreMigrationVersion": "8.0.0", "id": "76af8140-3e84-11ea-bb0a-69c3ca1d410f", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:33:58.419Z", "version": "WzQzNjEsMV0="}