{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "system.auth.ssh.event:Accepted"}}}, "title": "Successful SSH logins [Filebeat System] ECS", "uiStateJSON": {"vis": {"colors": {"Accepted": "#3F6833", "Failed": "#F9934E", "Invalid": "#447EBC", "password": "#BF1B00", "publickey": "#629E51"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "system.auth.ssh.method", "order": "desc", "orderBy": "1", "size": 5}, "schema": "group", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "defaultYExtents": false, "legendPosition": "right", "mode": "stacked", "scale": "linear", "setYExtents": false, "times": []}, "title": "Successful SSH logins ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "d16bb400-f9cc-11e6-8115-a7c18106d86a-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:23.956Z", "version": "WzQ1MzcsMV0="}