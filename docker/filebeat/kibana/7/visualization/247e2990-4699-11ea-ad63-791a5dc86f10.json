{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "title": "S3 Bucket Name Filter [Filebeat AWS]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"controls": [{"fieldName": "aws.s3.bucket.name", "id": "1565034367477", "indexPatternRefName": "control_0_index_pattern", "label": "S3 Bucket Names", "options": {"dynamicOptions": true, "multiselect": true, "order": "desc", "size": 5, "type": "terms"}, "parent": "", "type": "list"}], "pinFilters": false, "updateFiltersOnChange": true, "useTimeFilter": true}, "title": "S3 Bucket Name Filter [Filebeat AWS]", "type": "input_control_vis"}}, "coreMigrationVersion": "8.0.0", "id": "247e2990-4699-11ea-ad63-791a5dc86f10", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "control_0_index_pattern", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:00.470Z", "version": "WzQzNzQsMV0="}