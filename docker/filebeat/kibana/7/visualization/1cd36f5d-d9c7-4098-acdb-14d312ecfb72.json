{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"query_string": {"query": "*"}}}}, "title": "Ingress Interfaces (packets) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "listeners": {}, "params": {"expression": ".es(index=\"filebeat-*\", metric=\"sum:network.packets\", split=\"netflow.ingress_interface:10\", kibana=true).scale_interval(1s).fit(mode=scale).if(operator=\"lt\", if=0, then=0).trim(start=2,end=1).label(regex=\"^.* netflow.ingress_interface:(.+) > .*$\", label=\"$1\").lines(width=1, stack=true, fill=1).yaxis(label=\"packets / sec\", min=0)", "interval": "auto"}, "title": "Ingress Interfaces (packets) [Filebeat Netflow]", "type": "timelion"}}, "coreMigrationVersion": "8.0.0", "id": "1cd36f5d-d9c7-4098-acdb-14d312ecfb72", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:44.454Z", "version": "WzQ4MDgsMV0="}