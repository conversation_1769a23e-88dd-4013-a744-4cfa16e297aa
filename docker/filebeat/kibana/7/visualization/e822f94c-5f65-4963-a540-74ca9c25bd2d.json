{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Destinations and Sources (bytes) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "2", "params": {"customLabel": "Destination", "field": "destination.ip", "order": "desc", "orderBy": "1", "size": 50}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Source", "field": "source.ip", "order": "desc", "orderBy": "1", "size": 50}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}}, "title": "Destinations and Sources (bytes) [Filebeat Netflow]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "e822f94c-5f65-4963-a540-74ca9c25bd2d", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:43.370Z", "version": "WzQ3OTgsMV0="}