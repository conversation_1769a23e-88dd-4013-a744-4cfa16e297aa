{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "[ApexTrigger] Trigger-Entity categorisation [Filebeat Salesforce]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Trigger Name", "field": "salesforce.apex.trigger.name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}, {"enabled": true, "id": "3", "params": {"field": "salesforce.apex.entity_name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 200}, "position": "left", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": true, "valueAxis": "ValueAxis-1"}, "labels": {}, "legendPosition": "right", "maxLegendLines": 1, "palette": {"name": "default", "type": "palette"}, "radiusRatio": 0, "seriesParams": [{"circlesRadius": 3, "data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "normal", "show": true, "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "truncateLegend": true, "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": true, "rotate": 75, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "bottom", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "[ApexTrigger] Trigger-Entity categorisation [Filebeat Salesforce]", "type": "horizontal_bar"}}, "coreMigrationVersion": "7.15.0", "id": "24c39ae0-574d-11ec-8f0b-05e8b06e1b10", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2022-05-06T11:14:55.111Z", "version": "WzMzNDcxLDNd"}