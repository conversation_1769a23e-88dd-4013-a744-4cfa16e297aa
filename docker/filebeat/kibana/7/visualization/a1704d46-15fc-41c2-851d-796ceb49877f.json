{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"query_string": {"query": "*"}}}}, "title": "Types of Service (packets) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "listeners": {}, "params": {"expression": ".es(index=\"filebeat-*\", metric=\"sum:network.packets\", split=\"netflow.ip_class_of_service:10\", kibana=true).scale_interval(1s).fit(mode=scale).if(operator=\"lt\", if=0, then=0).trim(start=2,end=1).label(regex=\"^.* netflow.ip_class_of_service:(.+) > .*$\", label=\"$1\").lines(width=1, stack=true, fill=1).yaxis(label=\"packets / sec\", min=0)", "interval": "auto"}, "title": "Types of Service (packets) [Filebeat Netflow]", "type": "timelion"}}, "coreMigrationVersion": "8.0.0", "id": "a1704d46-15fc-41c2-851d-796ceb49877f", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:49.565Z", "version": "WzQ4NzksMV0="}