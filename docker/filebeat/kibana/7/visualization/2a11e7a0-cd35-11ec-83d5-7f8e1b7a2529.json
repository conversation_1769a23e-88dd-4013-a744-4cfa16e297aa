{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "title": "[Logout] Logout Over Time [Filebeat Salesforce]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"axis_formatter": "number", "axis_position": "left", "axis_scale": "normal", "drop_last_bucket": 0, "filter": {"language": "kuery", "query": "event.dataset : \"salesforce.logout\""}, "id": "9b373d2d-abc7-4c3e-a45a-b3fed48fa34f", "index_pattern_ref_name": "metrics_0_index_pattern", "interval": "", "isModelInvalid": false, "max_lines_legend": 1, "series": [{"axis_position": "right", "chart_type": "line", "color": "#68BC00", "fill": 0.5, "formatter": "number", "id": "0e7dc0ec-81a5-437b-a632-ff8b9a3f84d2", "line_width": 1, "metrics": [{"id": "df05f5a6-0774-43b7-ae50-1f4ce8cdbbdd", "type": "count"}], "palette": {"name": "default", "type": "palette"}, "point_size": 1, "separate_axis": 0, "split_mode": "everything", "stacked": "none"}], "show_grid": 1, "show_legend": 1, "time_field": "@timestamp", "time_range_mode": "entire_time_range", "tooltip_mode": "show_all", "truncate_legend": 1, "type": "timeseries", "use_kibana_indexes": true}, "title": "[Logout] Logout Over Time [Filebeat Salesforce]", "type": "metrics"}}, "coreMigrationVersion": "7.15.0", "id": "2a11e7a0-cd35-11ec-83d5-7f8e1b7a2529", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "metrics_0_index_pattern", "type": "index-pattern"}], "type": "visualization", "updated_at": "2022-05-06T12:07:56.442Z", "version": "WzMzOTQxLDNd"}