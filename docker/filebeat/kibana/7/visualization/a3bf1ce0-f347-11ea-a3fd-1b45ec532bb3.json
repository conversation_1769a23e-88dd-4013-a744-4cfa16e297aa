{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {}}, "title": "Ingress Controller Upstream Time Consumed By Path [Filebeat Nginx]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"axis_formatter": "number", "axis_position": "left", "axis_scale": "normal", "filter": {"language": "lucene", "query": "event.module: nginx AND fileset.name:ingress_controller"}, "id": "61ca57f0-469d-11e7-af02-69e470af7417", "index_pattern": "filebeat-*", "interval": "auto", "isModelInvalid": false, "legend_position": "bottom", "series": [{"axis_position": "right", "chart_type": "line", "color": "#68BC00", "fill": 0.5, "formatter": "s,s,", "id": "61ca57f1-469d-11e7-af02-69e470af7417", "label": "", "line_width": 1, "metrics": [{"field": "nginx.ingress_controller.upstream.response.time", "id": "61ca57f2-469d-11e7-af02-69e470af7417", "type": "sum"}], "point_size": 1, "seperate_axis": 0, "split_color_mode": "gradient", "split_filters": [{"color": "#68BC00", "filter": {"language": "lucene", "query": "http.response.status_code:[200 TO 299]"}, "id": "7c343c20-a29e-11e7-a062-a1c3587f4874", "label": "200s"}], "split_mode": "terms", "stacked": "none", "terms_field": "url.original", "type": "timeseries"}], "show_grid": 1, "show_legend": 1, "time_field": "@timestamp", "type": "timeseries", "use_kibana_indexes": false}, "title": "Ingress Controller Upstream Time Consumed By Path [Filebeat Nginx]", "type": "metrics"}}, "coreMigrationVersion": "8.0.0", "id": "a3bf1ce0-f347-11ea-a3fd-1b45ec532bb3", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:17.806Z", "version": "WzQ0OTYsMV0="}