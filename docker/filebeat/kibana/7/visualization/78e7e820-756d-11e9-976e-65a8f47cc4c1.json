{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.action", "negate": true, "params": {"query": "flow_terminated"}, "type": "phrase", "value": "flow_terminated"}, "query": {"match": {"event.action": {"query": "flow_terminated", "type": "phrase"}}}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Flow Creation Histogram [Filebeat PANW] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1, "timeRange": {"from": "2018-04-10T04:36:19.586Z", "to": "2018-04-10T04:39:56.264Z"}, "useNormalizedEsInterval": true}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "event.outcome", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false}, "isVislibVis": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "mode": "stacked", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Flow Creation Histogram [Filebeat PANW] ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "78e7e820-756d-11e9-976e-65a8f47cc4c1", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "290685e0-7569-11e9-976e-65a8f47cc4c1", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:20.886Z", "version": "WzQ1MTcsMV0="}