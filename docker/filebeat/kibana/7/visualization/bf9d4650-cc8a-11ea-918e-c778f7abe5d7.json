{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": "Denied Destination IPs", "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.action", "negate": false, "params": {"query": "denied"}, "type": "phrase"}, "query": {"match_phrase": {"event.action": "denied"}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.action: \"denied\" "}}}, "title": "Denied Destination IPs [Filebeat Pensando]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "server.ip", "json": "", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 25}, "schema": "segment", "type": "terms"}], "params": {"maxFontSize": 36, "minFontSize": 14, "orientation": "single", "palette": {"name": "kibana_palette", "type": "palette"}, "scale": "linear", "showLabel": false}, "title": "Denied Destination IPs [Filebeat Pensando]", "type": "tagcloud"}}, "coreMigrationVersion": "8.0.0", "id": "bf9d4650-cc8a-11ea-918e-c778f7abe5d7", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:56.763Z", "version": "WzQ5NDUsMV0="}