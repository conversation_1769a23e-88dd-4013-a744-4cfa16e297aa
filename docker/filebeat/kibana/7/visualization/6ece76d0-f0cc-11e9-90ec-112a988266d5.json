{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset :\"azure.activitylogs\" "}}}, "title": "Caller IP [Filebeat Azure]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": null, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "2", "params": {"customLabel": "Caller IP", "field": "source.ip", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "5", "otherBucket": false, "otherBucketLabel": "Other", "size": 100}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Country", "field": "geo.country_name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "5", "otherBucket": false, "otherBucketLabel": "Other", "size": 100}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "4", "params": {"customLabel": "Email", "field": "azure.activitylogs.identity.claims_initiated_by_user.name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "_key", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "5", "params": {}, "schema": "metric", "type": "count"}], "params": {"dimensions": {"buckets": [{"accessor": 0, "aggType": "terms", "format": {"id": "terms", "params": {"id": "ip", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}, {"accessor": 1, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}, {"accessor": 2, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}], "metrics": [{"accessor": 3, "aggType": "count", "format": {"id": "number"}, "params": {}}]}, "perPage": 10, "percentageCol": "", "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Caller IP [Filebeat Azure]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "6ece76d0-f0cc-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:03.514Z", "version": "WzQ0MDQsMV0="}