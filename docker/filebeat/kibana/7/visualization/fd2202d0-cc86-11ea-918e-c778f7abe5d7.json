{"attributes": {"description": "Inner ring is client IP, middle ring is server IP and the outer ring is <PERSON>ow vs Deny actions performed by the FW", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Client to Server FW Action [Filebeat Pensando]", "uiStateJSON": {"vis": {"legendOpen": false}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "client.ip", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 100}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"field": "server.ip", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "4", "params": {"field": "event.action", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "labels": {"last_level": true, "show": false, "truncate": 100, "values": false}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Client to Server FW Action [Filebeat Pensando]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "fd2202d0-cc86-11ea-918e-c778f7abe5d7", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:56.763Z", "version": "WzQ5NDcsMV0="}