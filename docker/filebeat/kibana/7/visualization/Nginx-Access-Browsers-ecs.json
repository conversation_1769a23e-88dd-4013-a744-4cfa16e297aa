{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Browsers breakdown [Filebeat Nginx] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "user_agent.name", "order": "desc", "orderBy": "1", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"field": "user_agent.version", "order": "desc", "orderBy": "1", "size": 5}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "legendPosition": "bottom", "palette": {"name": "kibana_palette", "type": "palette"}, "shareYAxis": true}, "title": "Nginx Access Browsers ECS", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "Nginx-Access-Browsers-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:19.828Z", "version": "WzQ1MDQsMV0="}