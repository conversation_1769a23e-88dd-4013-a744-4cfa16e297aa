{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Top Destinations Table [Filebeat Netflow]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": null, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "2", "params": {"customLabel": "Packets", "field": "network.packets"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "3", "params": {"customLabel": "Duration", "field": "event.duration"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "4", "params": {"customLabel": "Destination IP", "field": "destination.ip", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 30}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "5", "params": {"customLabel": "Domain", "field": "destination.domain", "missingBucket": true, "missingBucketLabel": "", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 1}, "schema": "bucket", "type": "terms"}], "params": {"perPage": 10, "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Top Destinations Table [Filebeat Netflow]", "type": "table"}}, "coreMigrationVersion": "7.15.0", "id": "8d0c61f0-1ae6-11e9-9eb0-d1ab52900288", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "c1e2ccd0-1ae5-11e9-9eb0-d1ab52900288", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-09-06T13:06:21.081Z", "version": "WzUyNzYsMV0="}