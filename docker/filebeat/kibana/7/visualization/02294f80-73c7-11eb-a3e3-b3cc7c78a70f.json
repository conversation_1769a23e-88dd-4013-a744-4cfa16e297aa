{"attributes": {"description": "MISP SHA1 hash indicators ingested by the threat intel Filebeat module.", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "threatintel.misp"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "threatintel.misp"}}}, {"$state": {"store": "appState"}, "exists": {"field": "threat.indicator.file.hash.sha1"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "threat.indicator.file.hash.sha1", "negate": false, "type": "exists", "value": "exists"}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "MISP SHA1 Hash Indicators [Filebeat Threat Intel]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "MISP SHA1 Hash Indicator", "field": "threat.indicator.file.hash.sha1", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 10}, "schema": "bucket", "type": "terms"}], "params": {"perPage": 10, "percentageCol": "", "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": false, "showTotal": false, "totalFunc": "sum"}, "title": "MISP SHA1 Hash Indicators [Filebeat Threat Intel]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "02294f80-73c7-11eb-a3e3-b3cc7c78a70f", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}, {"id": "6acbb070-72d0-11eb-a3e3-b3cc7c78a70f", "name": "search_0", "type": "search"}, {"id": "d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "name": "tag-d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "type": "tag"}], "type": "visualization", "updated_at": "2021-08-04T16:34:31.083Z", "version": "WzQ2NDEsMV0="}