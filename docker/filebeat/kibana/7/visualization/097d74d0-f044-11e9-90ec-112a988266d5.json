{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "title": "Subscriptions Filter [Filebeat Azure]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"controls": [{"fieldName": "azure.subscription_id", "id": "1571250866125", "indexPatternRefName": "control_0_index_pattern", "label": "Subscription ID", "options": {"dynamicOptions": true, "multiselect": true, "order": "desc", "size": 5, "type": "terms"}, "parent": "", "type": "list"}], "pinFilters": false, "updateFiltersOnChange": true, "useTimeFilter": false}, "title": "Subscriptions Filter [Filebeat Azure]", "type": "input_control_vis"}}, "coreMigrationVersion": "8.0.0", "id": "097d74d0-f044-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "control_0_index_pattern", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:02.545Z", "version": "WzQzODcsMV0="}