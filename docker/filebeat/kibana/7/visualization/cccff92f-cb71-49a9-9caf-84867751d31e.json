{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Top Flow Exporters [Filebeat Netflow]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": null, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "2", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "3", "params": {"customLabel": "Packets", "field": "network.packets"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "1", "params": {"customLabel": "Flow Records"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "4", "params": {"customLabel": "Flow Exporter", "field": "agent.name", "order": "desc", "orderBy": "2", "size": 500}, "schema": "bucket", "type": "terms"}], "listeners": {}, "params": {"perPage": 10, "showMeticsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": true, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Top Flow Exporters [Filebeat Netflow]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "cccff92f-cb71-49a9-9caf-84867751d31e", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:48.527Z", "version": "WzQ4NDQsMV0="}