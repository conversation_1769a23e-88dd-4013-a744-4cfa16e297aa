{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Code Signers [Filebeat Santa] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "certificate.common_name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"maxFontSize": 39, "minFontSize": 12, "orientation": "single", "palette": {"name": "kibana_palette", "type": "palette"}, "scale": "linear", "showLabel": true}, "title": "Code Signers [Filebeat Santa] ECS", "type": "tagcloud"}}, "coreMigrationVersion": "8.0.0", "id": "11858000-ff6d-11e8-93c5-d5ecd1b3e307-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "6d56a010-ff6a-11e8-93c5-d5ecd1b3e307-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:51.587Z", "version": "WzQ5MDAsMV0="}