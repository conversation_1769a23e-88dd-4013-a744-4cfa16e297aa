{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "logs over time [Filebeat Logstash] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "log.level", "order": "desc", "orderBy": "1", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false, "style": {"color": "#eee"}}, "isVislibVis": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "mode": "stacked", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "logs over time [Filebeat Logstash] ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "e90b7240-cbda-11e7-9852-73e0a9df1bb6-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "cfaba090-cbda-11e7-9852-73e0a9df1bb6-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:11.672Z", "version": "WzQ0NDIsMV0="}