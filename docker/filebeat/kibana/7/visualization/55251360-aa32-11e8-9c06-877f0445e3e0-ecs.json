{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Backend breakdown [Filebeat HAProxy] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "haproxy.backend_name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "labels": {"last_level": true, "show": false, "truncate": 100, "values": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Backend breakdown [Filebeat HAProxy] ECS", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "55251360-aa32-11e8-9c06-877f0445e3e0-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:05.531Z", "version": "WzQ0MTMsMV0="}