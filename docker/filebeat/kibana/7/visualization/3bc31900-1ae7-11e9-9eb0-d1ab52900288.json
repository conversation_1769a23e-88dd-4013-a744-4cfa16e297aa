{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Source Port and Transport [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Transport", "field": "network.transport", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Source Port", "field": "source.port", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 15}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "labels": {"last_level": true, "show": false, "truncate": 100, "values": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Source Port and Transport [Filebeat Netflow]", "type": "pie"}}, "coreMigrationVersion": "7.15.0", "id": "3bc31900-1ae7-11e9-9eb0-d1ab52900288", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "c1e2ccd0-1ae5-11e9-9eb0-d1ab52900288", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-09-06T13:06:21.081Z", "version": "WzUyNzIsMV0="}