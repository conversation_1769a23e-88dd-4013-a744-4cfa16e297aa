{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "activemq.audit"}, "type": "phrase", "value": "activemq.audit"}, "query": {"match": {"event.dataset": {"query": "activemq.audit", "type": "phrase"}}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Audit Account Tag Cloud [Filebeat ActiveMQ]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "activemq.user", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 15}, "schema": "segment", "type": "terms"}], "params": {"bucket": {"accessor": 0, "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "type": "vis_dimension"}, "maxFontSize": 72, "metric": {"accessor": 1, "format": {"id": "string", "params": {}}, "type": "vis_dimension"}, "minFontSize": 18, "orientation": "single", "palette": {"name": "kibana_palette", "type": "palette"}, "scale": "linear", "showLabel": true}, "title": "Audit Account Tag Cloud [Filebeat ActiveMQ]", "type": "tagcloud"}}, "coreMigrationVersion": "8.0.0", "id": "********-1454-11ea-8fd8-030a13064883", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:33:54.354Z", "version": "WzQzMzUsMV0="}