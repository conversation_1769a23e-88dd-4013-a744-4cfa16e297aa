{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "postgresql.log"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "postgresql.log"}}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Log Level Count [Filebeat PostgreSQL] ECS", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": null, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "log.level", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 12}, "schema": "bucket", "type": "terms"}], "params": {"perPage": 10, "percentageCol": "", "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Log Level Count [Filebeat PostgreSQL] ECS", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "PostgreSQL Log Level Count-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "PostgreSQL All Logs-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:33:50.312Z", "version": "WzQzMTMsMV0="}