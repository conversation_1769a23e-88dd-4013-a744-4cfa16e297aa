{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "DFW Allowed Count [Filebeat Pensando]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": ""}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "", "exclude": "denied", "field": "event.action", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": false, "addTooltip": true, "metric": {"colorSchema": "Green to Red", "colorsRange": [{"from": 0, "to": 10000}], "invertColors": false, "labels": {"show": false}, "metricColorMode": "None", "percentageMode": false, "style": {"bgColor": false, "bgFill": "#000", "fontSize": 30, "labelColor": false, "subText": ""}, "useRanges": false}, "type": "metric"}, "title": "DFW Allowed Count [Filebeat Pensando]", "type": "metric"}}, "coreMigrationVersion": "8.0.0", "id": "fa745d10-cc88-11ea-918e-c778f7abe5d7", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:56.763Z", "version": "WzQ5NDMsMV0="}