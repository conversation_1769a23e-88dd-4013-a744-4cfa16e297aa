{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "CloudTrail Event Outcome over time [Filebeat AWS]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1, "scaleMetricValues": false, "timeRange": {"from": "now-24h", "to": "now"}, "useNormalizedEsInterval": true}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "event.outcome", "missingBucket": true, "missingBucketLabel": "[unknown]", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "fittingFunction": "zero", "grid": {"categoryLines": false}, "isVislibVis": true, "labels": {}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "stacked", "show": true, "showCircles": true, "type": "area", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "type": "area", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "CloudTrail Event Outcome over time [Filebeat AWS]", "type": "area"}}, "coreMigrationVersion": "8.0.0", "id": "4c23e4c0-739a-11ea-a345-f985c61fe654", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "30ccde50-7397-11ea-a345-f985c61fe654", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:35.213Z", "version": "WzQ2OTAsMV0="}