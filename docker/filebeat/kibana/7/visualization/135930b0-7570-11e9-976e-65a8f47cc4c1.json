{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.action", "negate": true, "params": {"query": "flow_started"}, "type": "phrase", "value": "flow_started"}, "query": {"match": {"event.action": {"query": "flow_started", "type": "phrase"}}}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Destination Zone breakout [Filebeat PANW] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "panw.panos.destination.zone", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"field": "event.outcome", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 200}, "position": "left", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false}, "isVislibVis": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "mode": "normal", "show": true, "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": true, "rotate": 75, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "bottom", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Destination Zone breakout [Filebeat PANW] ECS", "type": "horizontal_bar"}}, "coreMigrationVersion": "8.0.0", "id": "135930b0-7570-11e9-976e-65a8f47cc4c1", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "290685e0-7569-11e9-976e-65a8f47cc4c1", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:20.886Z", "version": "WzQ1MTksMV0="}