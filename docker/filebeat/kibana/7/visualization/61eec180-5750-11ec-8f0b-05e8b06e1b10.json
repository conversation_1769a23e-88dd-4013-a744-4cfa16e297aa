{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "salesforce.apex"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "salesforce.apex"}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Response categorization by user agent [Filebeat Salesforce]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "salesforce.apex.user_agent", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 20}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false}, "labels": {"show": false}, "legendPosition": "right", "maxLegendLines": 1, "palette": {"name": "default", "type": "palette"}, "radiusRatio": 0, "seriesParams": [{"circlesRadius": 3, "data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "stacked", "show": true, "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "truncateLegend": true, "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Response categorization by user agent [Filebeat Salesforce]", "type": "histogram"}}, "coreMigrationVersion": "7.15.0", "id": "61eec180-5750-11ec-8f0b-05e8b06e1b10", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2022-05-06T11:14:55.111Z", "version": "WzMzNDc2LDNd"}