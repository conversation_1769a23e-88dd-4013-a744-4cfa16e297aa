{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": []}}, "savedSearchRefName": "search_0", "title": "Endpoint Metrics Overview [Filebeat CEF]", "uiStateJSON": {"vis": {"defaultColors": {"0 - 100": "rgb(0,104,55)"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Event Count"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Devices", "field": "observer.hostname"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "3", "params": {"customLabel": "Source", "field": "source.ip"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "4", "params": {"customLabel": "Destination", "field": "destination.ip"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "5", "params": {"customLabel": "Port", "field": "destination.port"}, "schema": "metric", "type": "cardinality"}], "listeners": {}, "params": {"addLegend": false, "addTooltip": true, "fontSize": "30", "gauge": {"autoExtend": false, "backStyle": "Full", "colorSchema": "Green to Red", "colorsRange": [{"from": 0, "to": 100}], "gaugeColorMode": "None", "gaugeStyle": "Full", "gaugeType": "Metric", "invertColors": false, "labels": {"color": "black", "show": true}, "orientation": "vertical", "percentageMode": false, "scale": {"color": "#333", "labels": false, "show": false, "width": 2}, "style": {"bgColor": false, "bgFill": "#000", "fontSize": "12", "labelColor": false, "subText": ""}, "type": "simple", "useRange": false, "verticalSplit": false}, "handleNoResults": true, "type": "gauge"}, "title": "Endpoint Metrics Overview [Filebeat CEF]", "type": "metric"}}, "coreMigrationVersion": "8.0.0", "id": "7454c034-c5f3-48fe-8fce-ef4385c80350", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "5cede2d3-20fe-4140-add4-4c4f841b71a2", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:37.252Z", "version": "WzQ3MjEsMV0="}