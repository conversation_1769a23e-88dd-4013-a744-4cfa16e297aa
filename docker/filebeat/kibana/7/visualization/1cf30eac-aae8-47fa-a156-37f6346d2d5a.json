{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Types of Service (bytes) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "2", "params": {"customLabel": "Type of Service", "field": "netflow.ip_class_of_service", "order": "desc", "orderBy": "1", "size": 50}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}}, "title": "Types of Service (bytes) [Filebeat Netflow]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "1cf30eac-aae8-47fa-a156-37f6346d2d5a", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:49.565Z", "version": "WzQ4NjEsMV0="}