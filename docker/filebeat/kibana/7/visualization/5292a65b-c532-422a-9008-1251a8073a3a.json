{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Top Cities [Filebeat Netflow]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": 2, "direction": "desc"}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "2", "params": {"customLabel": "Bytes", "field": "network.bytes"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "3", "params": {"customLabel": "Packets", "field": "network.packets"}, "schema": "metric", "type": "sum"}, {"enabled": true, "id": "1", "params": {"customLabel": "Flow Records"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "4", "params": {"customLabel": "Country", "field": "destination.geo.country_name", "order": "desc", "orderBy": "2", "size": 500}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "5", "params": {"customLabel": "City", "field": "destination.geo.city_name", "order": "desc", "orderBy": "2", "size": 500}, "schema": "bucket", "type": "terms"}], "listeners": {}, "params": {"perPage": 10, "showMeticsAtAllLevels": false, "showPartialRows": true, "showToolbar": true, "showTotal": true, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Top Cities [Filebeat Netflow]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "5292a65b-c532-422a-9008-1251a8073a3a", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:48.527Z", "version": "WzQ4NDMsMV0="}