{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"query_string": {"analyze_wildcard": true, "query": "*"}}}}, "title": "Flow Records [Filebeat Netflow]", "uiStateJSON": {"vis": {"legendOpen": true}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Flow Records"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Timeline", "extended_bounds": {}, "field": "event.end", "interval": "s", "min_doc_count": 1}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"customLabel": "Version", "field": "netflow.exporter.version", "order": "desc", "orderBy": "1", "size": 5}, "schema": "group", "type": "terms"}], "listeners": {}, "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "defaultYExtents": false, "grid": {"categoryLines": false, "style": {"color": "#eee"}}, "legendPosition": "right", "mode": "stacked", "scale": "linear", "seriesParams": [{"data": {"id": "1", "label": "Flow Records"}, "drawLinesBetweenPoints": true, "mode": "stacked", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "setYExtents": false, "times": [], "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Flow Records [Filebeat Netflow]", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "4bb0255e-18ed-45e4-bfb9-de8e35b12094", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:45.451Z", "version": "WzQ4MTEsMV0="}