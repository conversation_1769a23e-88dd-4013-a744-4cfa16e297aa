{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "lucene", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Errors per code, queue manager and host [Filebeat IBM MQ]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Errorcodes", "field": "ibmmq.errorlog.code", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": true, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Queue Manager", "field": "ibmmq.errorlog.qmgr", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "4", "params": {"customLabel": "Host", "field": "host.name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "dimensions": {"metric": {"accessor": 0, "aggType": "count", "format": {"id": "number"}, "params": {}}}, "distinctColors": true, "isDonut": true, "labels": {"last_level": true, "show": false, "truncate": 100, "values": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Errors per code, queue manager and host [Filebeat IBM MQ]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "df35c4b0-adf0-11e9-8358-1517661d7c84", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "82db7ba0-adec-11e9-8358-1517661d7c84", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:33:43.104Z", "version": "WzQyNjgsMV0="}