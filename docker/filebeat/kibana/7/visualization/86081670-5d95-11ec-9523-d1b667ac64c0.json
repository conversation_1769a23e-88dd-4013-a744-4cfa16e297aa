{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "salesforce.apex"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "salesforce.apex"}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "[Apex] DB_TOTAL_TIME-CPU_TIME comparision [Filebeat Salesforce]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1, "scaleMetricValues": false, "timeRange": {"from": "now-7d/d", "to": "now"}, "useNormalizedEsInterval": true, "used_interval": "3h"}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "salesforce.apex.cpu_time", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}, {"enabled": true, "id": "4", "params": {"field": "salesforce.apex.db_total_time", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "fittingFunction": "linear", "grid": {"categoryLines": true, "valueAxis": "ValueAxis-1"}, "labels": {}, "legendPosition": "right", "maxLegendLines": 1, "palette": {"name": "default", "type": "palette"}, "radiusRatio": 9, "seriesParams": [{"circlesRadius": 1, "data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "stacked", "show": true, "showCircles": true, "type": "area", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "truncateLegend": true, "type": "area", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": ""}, "type": "value"}]}, "title": "[Apex] DB_TOTAL_TIME-CPU_TIME comparision [Filebeat Salesforce]", "type": "area"}}, "coreMigrationVersion": "7.15.0", "id": "86081670-5d95-11ec-9523-d1b667ac64c0", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2022-05-06T11:14:55.111Z", "version": "WzMzNDcwLDNd"}