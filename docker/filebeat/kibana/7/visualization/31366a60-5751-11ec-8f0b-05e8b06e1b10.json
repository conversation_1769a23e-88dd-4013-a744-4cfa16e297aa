{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.action", "negate": false, "params": {"query": "apex-execution"}, "type": "phrase"}, "query": {"match_phrase": {"event.action": "apex-execution"}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "[ApexExecution] Apex Performance over time [Filebeat Salesforce]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Average Execution Time (ms)", "field": "event.duration"}, "schema": "metric", "type": "avg"}, {"enabled": true, "id": "2", "params": {"drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "m", "min_doc_count": 1, "scaleMetricValues": false, "timeRange": {"from": "now-7d/d", "to": "now"}, "useNormalizedEsInterval": true, "used_interval": "1h"}, "schema": "segment", "type": "date_histogram"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "fittingFunction": "linear", "grid": {"categoryLines": true, "valueAxis": "ValueAxis-1"}, "labels": {}, "legendPosition": "right", "maxLegendLines": 1, "palette": {"name": "default", "type": "palette"}, "radiusRatio": 9, "seriesParams": [{"circlesRadius": 3, "data": {"id": "1", "label": "Average Execution Time (ms)"}, "drawLinesBetweenPoints": true, "interpolate": "cardinal", "lineWidth": 2, "mode": "normal", "show": true, "showCircles": true, "type": "area", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "truncateLegend": true, "type": "line", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Average Execution Time (ms)"}, "type": "value"}]}, "title": "[ApexExecution] Apex Performance over time [Filebeat Salesforce]", "type": "line"}}, "coreMigrationVersion": "7.15.0", "id": "31366a60-5751-11ec-8f0b-05e8b06e1b10", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2022-05-06T11:14:55.111Z", "version": "WzMzNDc0LDNd"}