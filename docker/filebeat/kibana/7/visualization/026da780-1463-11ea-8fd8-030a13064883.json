{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "log.level", "negate": false, "params": {"query": "ERROR"}, "type": "phrase", "value": "ERROR"}, "query": {"match": {"log.level": {"query": "ERROR", "type": "phrase"}}}}, {"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "activemq.log"}, "type": "phrase", "value": "activemq.log"}, "query": {"match": {"event.dataset": {"query": "activemq.log", "type": "phrase"}}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Top ERROR callers [Filebeat ActiveMQ]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": null, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "3", "params": {"field": "activemq.caller", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 10}, "schema": "bucket", "type": "terms"}], "params": {"dimensions": {"buckets": [{"accessor": 0, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}], "metrics": [{"accessor": 1, "aggType": "count", "format": {"id": "number"}, "params": {}}]}, "perPage": 10, "percentageCol": "", "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Top ERROR callers [Filebeat ActiveMQ]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "026da780-1463-11ea-8fd8-030a13064883", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:33:53.367Z", "version": "WzQzMzEsMV0="}