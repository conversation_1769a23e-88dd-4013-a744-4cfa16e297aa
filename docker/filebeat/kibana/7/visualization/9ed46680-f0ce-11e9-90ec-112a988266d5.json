{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset :\"azure.activitylogs\" "}}}, "title": "Resource Type Breakdown [Filebeat Azure]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "azure.resource.provider", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 10}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "dimensions": {"buckets": [{"accessor": 0, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}], "metric": {"accessor": 1, "aggType": "count", "format": {"id": "number"}, "params": {}}}, "distinctColors": true, "isDonut": false, "labels": {"last_level": true, "show": false, "truncate": 100, "values": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Resource Type Breakdown [Filebeat Azure]", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "9ed46680-f0ce-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:03.514Z", "version": "WzQ0MDksMV0="}