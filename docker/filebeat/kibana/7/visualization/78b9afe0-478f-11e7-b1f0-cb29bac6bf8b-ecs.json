{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "highlightAll": true, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset:redis.log"}, "version": true}}, "title": "Log levels and roles breakdown [Filebeat Redis] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "3", "params": {"field": "redis.log.role", "order": "desc", "orderBy": "1", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "2", "params": {"customLabel": "Log level", "field": "log.level", "order": "desc", "orderBy": "1", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": false, "legendPosition": "bottom", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Log levels and roles breakdown [Filebeat Redis] ECS", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "78b9afe0-478f-11e7-b1f0-cb29bac6bf8b-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:22.899Z", "version": "WzQ1MzEsMV0="}