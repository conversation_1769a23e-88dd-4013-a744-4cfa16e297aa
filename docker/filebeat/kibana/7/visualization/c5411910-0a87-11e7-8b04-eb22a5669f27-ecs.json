{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Event Account Tag Cloud [Filebeat Auditd] ECS", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "user.name", "order": "desc", "orderBy": "1", "size": 15}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"hideLabel": false, "maxFontSize": 42, "minFontSize": 15, "orientation": "single", "palette": {"name": "kibana_palette", "type": "palette"}, "scale": "linear"}, "title": "Audit Event Account Tag Cloud ECS", "type": "tagcloud"}}, "coreMigrationVersion": "8.0.0", "id": "c5411910-0a87-11e7-8b04-eb22a5669f27-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:33:56.442Z", "version": "WzQzNTAsMV0="}