{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"query_string": {"query": "*"}}}}, "title": "Destination Autonomous Systems (packets) [Filebeat Netflow]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "listeners": {}, "params": {"expression": ".es(index=\"filebeat-*\", metric=\"sum:network.packets\", split=\"destination.as.organization.name:10\", kibana=true).scale_interval(1s).fit(mode=scale).if(operator=\"lt\", if=0, then=0).trim(start=2,end=1).label(regex=\"^.* destination.as.organization.name:(.+) > .*$\", label=\"$1\").lines(width=1, stack=true, fill=1).yaxis(label=\"packets / sec\", min=0)", "interval": "auto"}, "title": "Destination Autonomous Systems (packets) [Filebeat Netflow]", "type": "timelion"}}, "coreMigrationVersion": "8.0.0", "id": "d27b5d74-b3b4-4311-a0e6-08ff8f4345df", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:42.347Z", "version": "WzQ3ODksMV0="}