{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Decision and Reason [Filebeat Santa] ECS", "uiStateJSON": {"vis": {"colors": {"ALLOW": "#7EB26D"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Decision", "field": "santa.decision", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Reason", "field": "santa.reason", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "labels": {"last_level": true, "show": false, "truncate": 100, "values": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Decision and Reason [Filebeat Santa] ECS", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "30962fe0-ff6c-11e8-93c5-d5ecd1b3e307-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "6d56a010-ff6a-11e8-93c5-d5ecd1b3e307-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:51.587Z", "version": "WzQ4OTgsMV0="}