{"attributes": {"description": "Abuse URL threat of indicators ingested by the threat intel Filebeat module.", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "threatintel.abuseurl"}, "type": "phrase"}, "query": {"match_phrase": {"event.dataset": "threatintel.abuseurl"}}}, {"$state": {"store": "appState"}, "exists": {"field": "abusech.url.threat"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "key": "abusech.url.threat", "negate": false, "type": "exists", "value": "exists"}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Abuse URL Threat [Filebeat Threat Intel]", "uiStateJSON": {"vis": {"colors": {"Count": "#E24D42"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Abuse URL Threat", "field": "abusech.url.threat", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 200}, "position": "left", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "grid": {"categoryLines": false}, "isVislibVis": true, "labels": {"show": true}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "lineWidth": 2, "mode": "normal", "show": true, "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": true, "rotate": 75, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "bottom", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Abuse URL Threat [Filebeat Threat Intel]", "type": "horizontal_bar"}}, "coreMigrationVersion": "8.0.0", "id": "87980f70-72ec-11eb-a3e3-b3cc7c78a70f", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[1].meta.index", "type": "index-pattern"}, {"id": "6acbb070-72d0-11eb-a3e3-b3cc7c78a70f", "name": "search_0", "type": "search"}, {"id": "d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "name": "tag-d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "type": "tag"}], "type": "visualization", "updated_at": "2021-08-04T16:34:25.974Z", "version": "WzQ1NTAsMV0="}