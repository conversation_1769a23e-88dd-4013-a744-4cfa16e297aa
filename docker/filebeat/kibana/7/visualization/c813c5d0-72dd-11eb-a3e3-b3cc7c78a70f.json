{"attributes": {"description": "Timeline of indicators by type ingested by the threat intel Filebeat module.", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "exists": {"field": "threat.indicator.type"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "threat.indicator.type", "negate": false, "type": "exists", "value": "exists"}}], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Indicator Ingest Timeline per Type [Filebeat Threat Intel]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Count of Indicator by Type"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Ingest Timestamp", "drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1, "scaleMetricValues": false, "timeRange": {"from": "now-90d", "to": "now"}, "useNormalizedEsInterval": true}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "3", "params": {"field": "threat.indicator.type", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 10}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "fittingFunction": "zero", "grid": {"categoryLines": false}, "isVislibVis": true, "labels": {}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "seriesParams": [{"data": {"id": "1", "label": "Count of Indicator by Type"}, "drawLinesBetweenPoints": true, "interpolate": "linear", "lineWidth": 2, "mode": "stacked", "show": true, "showCircles": true, "type": "area", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#E7664C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "type": "area", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count of Indicator by Type"}, "type": "value"}]}, "title": "Indicator Ingest Timeline per Type [Filebeat Threat Intel]", "type": "area"}}, "coreMigrationVersion": "8.0.0", "id": "c813c5d0-72dd-11eb-a3e3-b3cc7c78a70f", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}, {"id": "6acbb070-72d0-11eb-a3e3-b3cc7c78a70f", "name": "search_0", "type": "search"}, {"id": "d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "name": "tag-d6ef8f20-70a9-11eb-a3e3-b3cc7c78a70f", "type": "tag"}], "type": "visualization", "updated_at": "2021-08-04T16:34:32.145Z", "version": "WzQ2NTgsMV0="}