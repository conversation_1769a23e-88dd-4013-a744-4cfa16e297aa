{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {}}, "title": "Service Health Overview [Filebeat Azure]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [], "params": {"axis_formatter": "number", "axis_position": "left", "axis_scale": "normal", "filter": {"language": "kuery", "query": "event.dataset :\"azure.activitylogs\" and azure.activitylogs.event_category : \"ServiceHealth\""}, "id": "61ca57f0-469d-11e7-af02-69e470af7417", "index_pattern": "filebeat-*", "interval": "", "isModelInvalid": false, "series": [{"axis_position": "right", "chart_type": "line", "color": "rgba(252,220,0,1)", "fill": 0.5, "filter": {"language": "kuery", "query": "azure.activitylogs.result_type: \"Active\""}, "formatter": "number", "hide_in_legend": 0, "id": "61ca57f1-469d-11e7-af02-69e470af7417", "line_width": 1, "metrics": [{"id": "61ca57f2-469d-11e7-af02-69e470af7417", "type": "count"}], "point_size": 1, "separate_axis": 0, "split_color_mode": "gradient", "split_mode": "filter", "stacked": "none"}, {"axis_position": "right", "chart_type": "line", "color": "#68BC00", "fill": 0.5, "filter": {"language": "kuery", "query": "azure.activitylogs.result_type: \"Resolved\" "}, "formatter": "number", "hide_in_legend": 0, "id": "5a52f170-ec1e-11e9-b6a7-21d19b63822a", "line_width": 1, "metrics": [{"id": "5a52f171-ec1e-11e9-b6a7-21d19b63822a", "type": "count"}], "point_size": 1, "separate_axis": 0, "split_color_mode": "gradient", "split_mode": "filter", "stacked": "none"}], "show_grid": 1, "show_legend": 0, "time_field": "", "type": "timeseries", "use_kibana_indexes": false}, "title": "Service Health Overview [Filebeat Azure]", "type": "metrics"}}, "coreMigrationVersion": "8.0.0", "id": "bc65e840-ec1e-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [], "type": "visualization", "updated_at": "2021-08-04T16:34:02.545Z", "version": "WzQzOTMsMV0="}