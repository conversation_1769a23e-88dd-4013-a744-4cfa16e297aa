{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": []}}, "savedSearchRefName": "search_0", "title": "Error logs over time [Filebeat MySQL] ECS", "uiStateJSON": {"vis": {"colors": {"Count": "#447EBC", "Error logs": "#1F78C1"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Error logs"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1}, "schema": "segment", "type": "date_histogram"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {"text": "@timestamp per 30 seconds"}, "type": "category"}], "defaultYExtents": false, "detailedTooltip": true, "grid": {"categoryLines": false, "style": {"color": "#eee"}}, "isVislibVis": true, "legendPosition": "right", "mode": "stacked", "palette": {"name": "kibana_palette", "type": "palette"}, "scale": "linear", "seriesParams": [{"data": {"id": "1", "label": "Error logs"}, "drawLinesBetweenPoints": true, "mode": "stacked", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "setYExtents": false, "shareYAxis": true, "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Error logs"}, "type": "value"}], "yAxis": {}}, "title": "Error logs over time [Filebeat MySQL] ECS", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "MySQL-error-logs-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "Filebeat-MySQL-error-log-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:13.657Z", "version": "WzQ0NTYsMV0="}