{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Ubiquiti Firewall Traffic Breakdown [Filebeat Iptables] ECS", "uiStateJSON": {"vis": {"colors": {"deny": "#E24D42", "icmp": "#F29191", "ipv4": "#65C5DB", "ipv6": "#D683CE", "ipv6-icmp": "#EA6460", "tcp": "#447EBC", "udp": "#F2C96D"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"field": "event.outcome", "missingBucket": true, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": true, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"field": "network.type", "missingBucket": true, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": true, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "4", "params": {"field": "network.transport", "missingBucket": true, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": true, "otherBucketLabel": "Other", "size": 5}, "schema": "segment", "type": "terms"}], "params": {"addLegend": true, "addTooltip": true, "distinctColors": true, "isDonut": true, "labels": {"last_level": false, "show": true, "truncate": 100, "values": false}, "legendPosition": "top", "palette": {"name": "kibana_palette", "type": "palette"}, "type": "pie"}, "title": "Ubiquiti Firewall Traffic Breakdown [Filebeat Iptables] ECS", "type": "pie"}}, "coreMigrationVersion": "8.0.0", "id": "fdea1ad0-1ff4-11e9-ae2a-939083c6a64e-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "c4e80aa0-1fd4-11e9-ae2a-939083c6a64e-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:33:45.146Z", "version": "WzQyODUsMV0="}