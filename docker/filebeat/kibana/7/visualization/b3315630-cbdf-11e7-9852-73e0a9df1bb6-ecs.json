{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "query": {"language": "kuery", "query": ""}}}, "savedSearchRefName": "search_0", "title": "Slowest plugins [Filebeat Logstash] ECS", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": 3, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "6", "params": {"customLabel": "Average", "field": "logstash.slowlog.took_in_millis"}, "schema": "metric", "type": "avg"}, {"enabled": true, "id": "5", "params": {"customLabel": "Min", "field": "logstash.slowlog.took_in_millis"}, "schema": "metric", "type": "min"}, {"enabled": true, "id": "8", "params": {"customLabel": "Plugin Name", "field": "logstash.slowlog.plugin_name", "order": "desc", "orderBy": "5", "size": 5}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "7", "params": {"customLabel": "Max", "field": "logstash.slowlog.took_in_millis"}, "schema": "metric", "type": "max"}, {"enabled": true, "id": "9", "params": {"customLabel": "Plugin Type", "field": "logstash.slowlog.plugin_type", "order": "desc", "orderBy": "5", "size": 5}, "schema": "bucket", "type": "terms"}], "params": {"perPage": 10, "showMeticsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Slowest plugins [Filebeat Logstash] ECS", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "b3315630-cbdf-11e7-9852-73e0a9df1bb6-ecs", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "742e45d0-cbdd-11e7-9852-73e0a9df1bb6-ecs", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2021-08-04T16:34:11.672Z", "version": "WzQ0NDMsMV0="}