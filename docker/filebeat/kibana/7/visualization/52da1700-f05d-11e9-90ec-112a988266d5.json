{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "lucene", "query": ""}}}, "title": "Users List [Filebeat Azure]", "uiStateJSON": {"vis": {"params": {"sort": {"columnIndex": null, "direction": null}}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "2", "params": {"customLabel": "Email", "field": "azure.activitylogs.identity.claims_initiated_by_user.name", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 20}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Name", "field": "azure.activitylogs.identity.claims_initiated_by_user.fullname", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 5}, "schema": "bucket", "type": "terms"}, {"enabled": true, "id": "5", "params": {"customLabel": "IPs", "field": "source.ip"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "1", "params": {"customLabel": "Actions"}, "schema": "metric", "type": "count"}], "params": {"dimensions": {"buckets": [{"accessor": 0, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}, {"accessor": 1, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}], "metrics": [{"accessor": 2, "aggType": "cardinality", "format": {"id": "number"}, "params": {}}, {"accessor": 3, "aggType": "count", "format": {"id": "number"}, "params": {}}]}, "perPage": 10, "percentageCol": "", "showMetricsAtAllLevels": false, "showPartialRows": false, "showToolbar": true, "showTotal": false, "sort": {"columnIndex": null, "direction": null}, "totalFunc": "sum"}, "title": "Users List [Filebeat Azure]", "type": "table"}}, "coreMigrationVersion": "8.0.0", "id": "52da1700-f05d-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:03.514Z", "version": "WzQ0MDMsMV0="}