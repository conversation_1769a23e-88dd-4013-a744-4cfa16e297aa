{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "envoyproxy.log"}, "type": "phrase", "value": "envoyproxy.log"}, "query": {"match": {"event.dataset": {"query": "envoyproxy.log", "type": "phrase"}}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Unique Domains [Filebeat Envoyproxy]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"field": "url.domain"}, "schema": "metric", "type": "cardinality"}], "params": {"addLegend": false, "addTooltip": true, "metric": {"bucket": {"accessor": 0, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}, "colorSchema": "Green to Red", "colorsRange": [{"from": 0, "to": 10000}], "invertColors": false, "labels": {"show": false}, "metricColorMode": "None", "metrics": [{"accessor": 0, "aggType": "cardinality", "format": {"id": "number"}, "params": {}}], "percentageMode": false, "style": {"bgColor": false, "bgFill": "#000", "fontSize": 60, "labelColor": false, "subText": ""}, "useRanges": false}, "type": "metric"}, "title": "Unique Domains [Filebeat Envoyproxy]", "type": "metric"}}, "coreMigrationVersion": "8.0.0", "id": "7e4084e0-5c99-11e9-8477-077ec9664dbd", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:33:42.085Z", "version": "WzQyNjAsMV0="}