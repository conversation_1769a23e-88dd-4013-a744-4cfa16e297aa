{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": "event.dataset : \"azure.activitylogs\" "}}}, "title": "Activity Stats [Filebeat Azure]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Resources", "field": "azure.resource.name"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "2", "params": {"customLabel": "Users", "field": "azure.activitylogs.identity.claims_initiated_by_user.name"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "3", "params": {"customLabel": "Resource Groups", "field": "azure.resource.group"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "4", "params": {"customLabel": "Subscriptions", "field": "azure.subscription_id"}, "schema": "metric", "type": "cardinality"}], "params": {"addLegend": false, "addTooltip": true, "dimensions": {"metrics": [{"accessor": 0, "format": {"id": "number", "params": {}}, "type": "vis_dimension"}, {"accessor": 1, "format": {"id": "number", "params": {}}, "type": "vis_dimension"}, {"accessor": 2, "format": {"id": "number", "params": {}}, "type": "vis_dimension"}]}, "metric": {"colorSchema": "Green to Red", "colorsRange": [{"from": 0, "to": 10000, "type": "range"}], "invertColors": false, "labels": {"show": true}, "metricColorMode": "None", "percentageMode": false, "style": {"bgColor": false, "bgFill": "#000", "fontSize": 60, "labelColor": false, "subText": ""}, "useRanges": false}, "type": "metric"}, "title": "Activity Stats [Filebeat Azure]", "type": "metric"}}, "coreMigrationVersion": "8.0.0", "id": "e4c7f4b0-f045-11e9-90ec-112a988266d5", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:34:02.545Z", "version": "WzQzODksMV0="}