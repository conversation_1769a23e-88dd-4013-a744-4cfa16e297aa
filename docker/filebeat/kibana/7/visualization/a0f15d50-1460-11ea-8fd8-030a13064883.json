{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": [{"$state": {"store": "appState"}, "meta": {"alias": null, "disabled": false, "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "key": "event.dataset", "negate": false, "params": {"query": "activemq.log"}, "type": "phrase", "value": "activemq.log"}, "query": {"match": {"event.dataset": {"query": "activemq.log", "type": "phrase"}}}}], "indexRefName": "kibanaSavedObjectMeta.searchSourceJSON.index", "query": {"language": "kuery", "query": ""}}}, "title": "Application Event Results [Filebeat ActiveMQ]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"drop_partials": false, "extended_bounds": {}, "field": "@timestamp", "interval": "auto", "min_doc_count": 1, "timeRange": {"from": "now-2d", "to": "now"}, "useNormalizedEsInterval": true}, "schema": "segment", "type": "date_histogram"}, {"enabled": true, "id": "4", "params": {"field": "log.level", "missingBucket": false, "missingBucketLabel": "Missing", "order": "desc", "orderBy": "1", "otherBucket": false, "otherBucketLabel": "Other", "size": 15}, "schema": "group", "type": "terms"}], "params": {"addLegend": true, "addTimeMarker": false, "addTooltip": true, "categoryAxes": [{"id": "CategoryAxis-1", "labels": {"filter": true, "show": true, "truncate": 100}, "position": "bottom", "scale": {"type": "linear"}, "show": true, "style": {}, "title": {}, "type": "category"}], "detailedTooltip": true, "dimensions": {"series": [{"accessor": 2, "aggType": "terms", "format": {"id": "terms", "params": {"id": "string", "missingBucketLabel": "Missing", "otherBucketLabel": "Other"}}, "params": {}}], "splitColumn": [{"accessor": 1, "aggType": "filters", "format": {}, "params": {}}], "x": {"accessor": 0, "aggType": "date_histogram", "format": {"id": "date", "params": {"pattern": "YYYY-MM-DD HH:mm"}}, "params": {"bounds": {"max": "2019-12-01T17:52:01.645Z", "min": "2019-11-29T17:52:01.645Z"}, "date": true, "format": "YYYY-MM-DD HH:mm", "interval": "PT1H"}}, "y": [{"accessor": 3, "aggType": "count", "format": {"id": "number"}, "params": {}}]}, "grid": {"categoryLines": false}, "isVislibVis": true, "labels": {"show": false}, "legendPosition": "right", "palette": {"name": "kibana_palette", "type": "palette"}, "radiusRatio": 50, "seriesParams": [{"data": {"id": "1", "label": "Count"}, "drawLinesBetweenPoints": true, "mode": "normal", "show": "true", "showCircles": true, "type": "histogram", "valueAxis": "ValueAxis-1"}], "thresholdLine": {"color": "#34130C", "show": false, "style": "full", "value": 10, "width": 1}, "times": [], "type": "histogram", "valueAxes": [{"id": "ValueAxis-1", "labels": {"filter": false, "rotate": 0, "show": true, "truncate": 100}, "name": "LeftAxis-1", "position": "left", "scale": {"mode": "normal", "type": "linear"}, "show": true, "style": {}, "title": {"text": "Count"}, "type": "value"}]}, "title": "Application Event Results [Filebeat ActiveMQ]", "type": "histogram"}}, "coreMigrationVersion": "8.0.0", "id": "a0f15d50-1460-11ea-8fd8-030a13064883", "migrationVersion": {"visualization": "7.14.0"}, "references": [{"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.index", "type": "index-pattern"}, {"id": "filebeat-*", "name": "kibanaSavedObjectMeta.searchSourceJSON.filter[0].meta.index", "type": "index-pattern"}], "type": "visualization", "updated_at": "2021-08-04T16:33:53.367Z", "version": "WzQzMzAsMV0="}