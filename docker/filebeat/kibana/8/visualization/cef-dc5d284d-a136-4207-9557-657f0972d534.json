{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": []}}, "savedSearchRefName": "search_0", "title": "Top 20 Behaviors by Outcome — ArcSight [Filebeat CEF]", "uiStateJSON": {}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "2", "params": {"customLabel": "Event Behavior", "field": "cef.extensions.categoryBehavior", "order": "desc", "orderBy": "1", "size": 20}, "schema": "segment", "type": "terms"}, {"enabled": true, "id": "3", "params": {"customLabel": "Event Outcome", "field": "cef.extensions.categoryOutcome", "order": "desc", "orderBy": "1", "size": 3}, "schema": "segment", "type": "terms"}], "listeners": {}, "params": {"addTooltip": true, "distinctColors": true, "isDonut": true, "legendDisplay": "show", "legendPosition": "right", "legendSize": "auto", "palette": {"name": "kibana_palette", "type": "palette"}}, "title": "Top 20 Behaviors by Outcome — ArcSight [Filebeat CEF]", "type": "pie"}}, "coreMigrationVersion": "8.3.3", "id": "cef-dc5d284d-a136-4207-9557-657f0972d534", "migrationVersion": {"visualization": "8.3.0"}, "references": [{"id": "cef-69d6e511-7744-429a-9aa4-ceae2222db94", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2022-08-24T00:29:51.550Z", "version": "WzI0NTQsMV0="}