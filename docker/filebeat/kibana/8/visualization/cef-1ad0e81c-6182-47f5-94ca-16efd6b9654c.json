{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": []}}, "savedSearchRefName": "search_0", "title": "Endpoint OS Metrics Overview [Filebeat CEF]", "uiStateJSON": {"vis": {"defaultColors": {"0 - 100": "rgb(0,104,55)"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "1", "params": {"customLabel": "Total Events"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "6", "params": {"customLabel": "Devices", "field": "observer.hostname"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "7", "params": {"customLabel": "Event Types", "field": "event.action"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "8", "params": {"customLabel": "Event Outcomes", "field": "event.outcome"}, "schema": "metric", "type": "cardinality"}], "listeners": {}, "params": {"addLegend": false, "addTooltip": true, "fontSize": "30", "gauge": {"autoExtend": false, "backStyle": "Full", "colorSchema": "Green to Red", "colorsRange": [{"from": 0, "to": 100}], "gaugeColorMode": "None", "gaugeStyle": "Full", "gaugeType": "Metric", "invertColors": false, "labels": {"color": "black", "show": true}, "orientation": "vertical", "percentageMode": false, "scale": {"color": "#333", "labels": false, "show": false, "width": 2}, "style": {"bgColor": false, "bgFill": "#000", "fontSize": "20", "labelColor": false, "subText": ""}, "type": "simple", "useRange": false, "verticalSplit": false}, "handleNoResults": true, "type": "gauge"}, "title": "Endpoint OS Metrics Overview [Filebeat CEF]", "type": "metric"}}, "coreMigrationVersion": "8.3.3", "id": "cef-1ad0e81c-6182-47f5-94ca-16efd6b9654c", "migrationVersion": {"visualization": "8.3.0"}, "references": [{"id": "cef-12352cbd-ccbb-4ede-a98b-c4c82a358516", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2022-08-24T00:29:51.550Z", "version": "WzIzNzMsMV0="}