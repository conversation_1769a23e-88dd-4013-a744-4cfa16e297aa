{"attributes": {"description": "", "kibanaSavedObjectMeta": {"searchSourceJSON": {"filter": []}}, "savedSearchRefName": "search_0", "title": "Device Metrics Overview [Filebeat CEF]", "uiStateJSON": {"vis": {"defaultColors": {"0 - 100": "rgb(0,104,55)"}}}, "version": 1, "visState": {"aggs": [{"enabled": true, "id": "8", "params": {"customLabel": "Event Count"}, "schema": "metric", "type": "count"}, {"enabled": true, "id": "4", "params": {"customLabel": "Devices", "field": "observer.hostname"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "5", "params": {"customLabel": "Sources", "field": "source.ip"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "6", "params": {"customLabel": "Destinations", "field": "destination.ip"}, "schema": "metric", "type": "cardinality"}, {"enabled": true, "id": "7", "params": {"customLabel": "Ports", "field": "destination.port"}, "schema": "metric", "type": "cardinality"}], "listeners": {}, "params": {"addLegend": false, "addTooltip": true, "fontSize": "30", "gauge": {"autoExtend": false, "backStyle": "Full", "colorSchema": "Green to Red", "colorsRange": [{"from": 0, "to": 100}], "gaugeColorMode": "None", "gaugeStyle": "Full", "gaugeType": "Metric", "invertColors": false, "labels": {"color": "black", "show": true}, "orientation": "vertical", "percentageMode": false, "scale": {"color": "#333", "labels": false, "show": false, "width": 2}, "style": {"bgColor": false, "bgFill": "#000", "fontSize": "12", "labelColor": false, "subText": ""}, "type": "simple", "useRange": false, "verticalSplit": false}, "handleNoResults": true, "type": "gauge"}, "title": "Device Metrics Overview [Filebeat CEF]", "type": "metric"}}, "coreMigrationVersion": "8.3.3", "id": "cef-09816af7-d246-4880-b4c8-1ace7ea35b3b", "migrationVersion": {"visualization": "8.3.0"}, "references": [{"id": "cef-d687d631-96df-4c49-99ee-9e51c7053084", "name": "search_0", "type": "search"}], "type": "visualization", "updated_at": "2022-08-24T00:29:51.550Z", "version": "WzIzNTksMV0="}