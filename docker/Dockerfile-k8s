# FROM registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0
FROM registry.yitaiyitai.com/library/bpmax-base:16.20.2-2.1
ADD nginx.conf /etc/nginx/nginx.conf
ADD bpmax.conf /etc/nginx/conf.d/bpmax.conf
ADD ProcFile /var/www/ProcFile
ADD entrypoint.sh /var/www/entrypoint.sh
ADD MicrosoftYaHei.ttf /usr/share/fonts/chinese/MicrosoftYaHei.ttf
COPY hpt-frontend /var/www/hpt-frontend
COPY hpt-mobile /var/www/hpt-mobile
COPY huanpingtong-server /var/www/huanpingtong-server
COPY bpmax-server /var/www/bpmax-server
# RUN apt update && apt install -y rsync
RUN /bin/bash -c "chmod 644 /usr/share/fonts/chinese/MicrosoftYaHei.ttf && fc-cache -fv && chmod 777 /var/www/entrypoint.sh"
RUN cd /usr/bin/ && curl -H 'Pragma: no-cache'  https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_runner/crun-all -o runner && chmod +x runner
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV PM2_COLORS 0
CMD ["/var/www/entrypoint.sh"]
EXPOSE 8080
WORKDIR /var/www
