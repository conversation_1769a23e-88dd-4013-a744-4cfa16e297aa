[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log

[program:nestjs-server]
command=node --max-old-space-size=8192 --expose-gc dist/src/main.js
directory=/app/server
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nestjs.err.log
stdout_logfile=/var/log/supervisor/nestjs.out.log

[program:python-mining-service]
command=python app/main.py
directory=/app/python-mining-service
environment=PYTHON_SERVICE_PORT="%(ENV_PYTHON_SERVICE_PORT)s"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/python-mining.err.log
stdout_logfile=/var/log/supervisor/python-mining.out.log

[program:nuxt-frontend]
command=node .output/server/index.mjs
directory=/app/client
environment=PORT="%(ENV_NUXT_PORT)s"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nuxt.err.log
stdout_logfile=/var/log/supervisor/nuxt.out.log
