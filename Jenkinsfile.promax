
pipeline {
    agent none

    parameters {
        booleanParam(
            name: 'TESTING',
            defaultValue: false,
            description: '测试跳过环节'
        )
        booleanParam(
            name: 'FORCE_REBUILD_RUNTIME',
            defaultValue: false,
            description: '强制重建基础镜像 promax-runtime'
        )
        string(
            name: 'DEPLOY_BRANCH',
            defaultValue: 'master',
            description: '部署分支'
        )
        string(
            name: 'IMAGE_TAG',
            defaultValue: '',
            description: '镜像标签（留空则使用BUILD_NUMBER）'
        )
        choice(
            name: 'SERVER_ALIAS',
            choices: ['nil','dev005','dev006'],
            description: '服务器别名'
        )
        choice(
            name: 'DEPLOYENV',
            choices: ['promaxtest.yitaiyitai.com'],
            description: '部署环境'
        )
        booleanParam(
            name: 'MAKE_TAG',
            defaultValue: 'false',
            description: '是否打标签'
        )
    }

    environment {
        COMMIT_HASH_promax = ''
        DEPLOY_SERVER_ip = ''
        DEPLOY_SERVER_port = ''
        DEPLOY_SERVER_domain = ''
        DEPLOY_SERVER_ip_origin = ''
        // CREATE、UPDATE、CREATE_DELETE
        DEPLOY_type = ''
        DEPLOY_CACHE_file = '/var/jenkins_home/deploy-records.data'
        SERVERS_CACHE_file = './servers/server_alias.data'
    }

    stages {
        stage('Set Name') {
            agent {
                label 'main'
            }
            // when {
            //     // 添加条件判断，例如：当SKIP_SET_NAME参数不为true时执行
            //     // 您可以根据需要修改这个条件
            //     expression { return params.SKIP_SET_NAME != true }
            // }
            steps {
                sh '''
                    id
                    echo ${SERVER_ALIAS}
                    echo ${DEPLOYENV}
                    cat ${SERVERS_CACHE_file}
                    cat ${DEPLOY_CACHE_file}

                    var_file_path=./tmpdata/${BUILD_NUMBER}
                    rm -rf "$var_file_path"
                    mkdir -p "$var_file_path"
                    chmod 777 -R "$var_file_path"
                    echo "======BUILD_NUMBER: ${BUILD_NUMBER} ======"
                    touch "${var_file_path}/DEPLOY_summary"



                    if [ "${OFFLINE}" = "false" ]; then
                        set +ex
                        deploy_record="$(cat "$DEPLOY_CACHE_file" | grep "$DEPLOYENV")"

                        if [ -z $deploy_record ]; then
                            echo "====== deploy record is nil ======"

                            if [ "$SERVER_ALIAS" = 'nil' ]; then
                                echo "====== SERVER_ALIAS is nil, deploy fail!!! ======"
                            else
                                echo ====== SERVER_ALIAS: $SERVER_ALIAS ======
                                DEPLOY_SERVER_ip=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" | awk -F"=" '{print $2}')
                                echo ====== DEPLOY_SERVER_ip: $DEPLOY_SERVER_ip ======
                                DEPLOY_type="CREATE"
                                DEPLOY_SERVER_domain=$DEPLOYENV
                            fi

                        else
                            echo "====== deploy record exist ======"
                            DEPLOY_SERVER_ip=$(echo $deploy_record | awk -F' ' '{print $1}')
                            DEPLOY_SERVER_port=$(echo $deploy_record | awk -F' ' '{print $2}')
                            DEPLOY_SERVER_domain=${DEPLOYENV}

                            if [ "$SERVER_ALIAS" = 'nil' ]; then

                                echo "====== SERVER_ALIAS is nil, deploy update!!! ======"
                                DEPLOY_type="UPDATE"

                            else

                                echo ====== SERVER_ALIAS exist: $SERVER_ALIAS, deploy create and delete!!! ======
                                DEPLOY_SERVER_ip_origin=$DEPLOY_SERVER_ip
                                DEPLOY_SERVER_ip=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" | awk -F"=" '{print $2}')
                                echo ====== DEPLOY_SERVER_ip_origin: $DEPLOY_SERVER_ip_origin ======
                                DEPLOY_type="CREATE_DELETE"

                                if [ "$DEPLOY_SERVER_ip_origin" = "$DEPLOY_SERVER_ip" ]; then
                                    echo "====== IP addresses are the same ======"
                                    DEPLOY_type="UPDATE"
                                fi

                            fi

                        fi

                        probe_port() {
                            # 参数1: 服务器IP
                            # 参数2: 需要的端口数量
                            local server_ip=$1
                            local port_count=$2
                            local available_ports=""
                            local port_count_found=0

                            # 在35000-36000范围内查找可用端口
                            for i in $(seq 35000 36000); do
                                echo "=== probe $server_ip $i ==="
                                result=$(timeout 1 telnet $server_ip $i 2>&1)
                                r=$(echo $result | grep -v "Escape character")
                                if [ -n "$r" ]; then
                                    # 端口未被占用，添加到可用端口列表
                                    if [ -z "$available_ports" ]; then
                                        available_ports="$i"
                                    else
                                        available_ports="$available_ports,$i"
                                    fi
                                    port_count_found=$((port_count_found + 1))
                                    echo "Port $i is available"

                                    # 如果已找到足够数量的可用端口，则退出循环
                                    if [ $port_count_found -eq $port_count ]; then
                                        break
                                    fi
                                fi
                            done

                            # 如果找到了足够数量的端口，则设置DEPLOY_SERVER_port变量
                            if [ $port_count_found -eq $port_count ]; then
                                DEPLOY_SERVER_port=$available_ports
                                echo "Found $port_count available ports: $DEPLOY_SERVER_port"
                                return 0
                            else
                                echo "Failed to find $port_count available ports, only found $port_count_found: $available_ports"
                                # 如果至少找到了一个端口，也返回
                                if [ $port_count_found -gt 0 ]; then
                                    DEPLOY_SERVER_port=$available_ports
                                    return 1
                                fi
                                return 2
                            fi
                        }

                        if [ "$DEPLOY_type" = "CREATE" -o "$DEPLOY_type" = "CREATE_DELETE" ]; then
                            echo "====== create port ======"

                            echo $DEPLOY_SERVER_ip | grep '192.168.31.'
                            if [ $? -eq 0 ]; then
                                # 传递服务器IP和需要的端口数量(2)
                                probe_port $DEPLOY_SERVER_ip 1
                            else
                                DEPLOY_SERVER_port="FILE_FREEZE"
                            fi

                        fi

                        echo "====== check important vars: ip $DEPLOY_SERVER_ip, port $DEPLOY_SERVER_port, domain $DEPLOY_SERVER_domain, ip_origin $DEPLOY_SERVER_ip_origin, type $DEPLOY_type ======" >> "${var_file_path}/DEPLOY_summary"
                        cat "${var_file_path}/DEPLOY_summary"

                        create_var_file() {
                            echo "DEBUG: Param 1: '$1'"
                            echo "DEBUG: Param 2: '$2'"
                            echo "DEBUG: Param 3: '$3'"
                            if [ -z "$3" ]; then
                                echo "val is nil"
                                return 0
                            fi
                            filename="${1}/${2}"
                            touch "$filename"
                            echo $3 > "$filename"
                            echo "SUCCESS create_var_file"
                        }

                        create_var_file $var_file_path DEPLOY_type $DEPLOY_type

                        create_var_file $var_file_path DEPLOY_SERVER_ip $DEPLOY_SERVER_ip

                        create_var_file $var_file_path DEPLOY_SERVER_port $DEPLOY_SERVER_port

                        create_var_file $var_file_path DEPLOY_SERVER_domain $DEPLOY_SERVER_domain

                        create_var_file $var_file_path DEPLOY_SERVER_ip_origin $DEPLOY_SERVER_ip_origin

                    fi
                '''

                script {
                    currentBuild.description = "DeployEnv: ${DEPLOYENV} \n" +
                        "magic-report: ${DEPLOY_BRANCH} \n" +
                        "tag: ${MAKE_TAG}"
                }
            }
        }

        // stage('Build magicreport') {

        //     agent {
        //         docker {
        //             label 'main'
        //             image 'registry.yitaiyitai.com/library/bpmax-base:22.14.0-2'
        //             args '-v /yarn_cache_magicreport:/usr/local/share/.cache/yarn -u 0:0'
        //             reuseNode true
        //         }
        //     }
        //     environment {
        //         API_HOST = '/api'
        //         JOB_PWD = ''
        //     }
        //     steps {
        //         sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'


        //         dir('magicreport') {
        //             script {
        //                 if (env.BRANCH_TYPE_magicreport.toBoolean()) {
        //                     checkout([
        //                         $class: 'GitSCM',
        //                         branches: [[name: "refs/heads/${branch_magicreport}"]],
        //                         userRemoteConfigs: [
        //                             [url: 'http://gitlabbot:<EMAIL>/bpmax/magic-report.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
        //                         ]
        //                     ])
        //                 } else {
        //                     checkout([
        //                         $class: 'GitSCM',
        //                         branches: [[name: "refs/tags/${branch_magicreport}"]],
        //                         userRemoteConfigs: [
        //                             [url: 'http://gitlabbot:<EMAIL>/bpmax/magic-report.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
        //                         ]
        //                     ])
        //                 }

        //                 JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
        //                 sh "git config --global --add safe.directory ${JOB_PWD}"
        //                 COMMIT_HASH_magicreport = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
        //                 echo "COMMIT_HASH_magicreport: ${COMMIT_HASH_magicreport}"
        //                 echo "JOB_PWD: ${JOB_PWD}"

        //                 sh """
        //                     echo "${COMMIT_HASH_magicreport}"
        //                     echo "${COMMIT_HASH_magicreport}" > ../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_magicreport
        //                     echo "Saved commit hash to ../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_magicreport"
        //                 """

        //                 // 设置为环境变量，使其在整个pipeline中可用
        //                 env.COMMIT_HASH_magicreport = COMMIT_HASH_magicreport

        //             }

        //             sh "echo 源地址:"
        //             sh "yarn config get registry"
        //             sh '''
        //                 cp ../envs/.env_${DEPLOYENV}_server ./server/.env
        //                 cp ../envs/.env_${DEPLOYENV}_client ./client/.env.production
        //                 ./deploy.sh --install-deps
        //                 ./deploy.sh --build-client
        //                 ./deploy.sh --build-server
        //                 cd ..
        //                 chown -R 1000:1000 ./magicreport
        //                 rm -rf ./docker/magicreport
        //             '''
        //         }

        //     }
        // }

        // stage('Build Docker Image') {
        //     agent {
        //         label 'main'
        //     }
        //     steps {
        //         dir('docker') {
        //             sh "ls -la"
        //             sh '''
        //                 TAG=`date +%Y%m%d%H%M`
        //                 var_file_path=../tmpdata/${BUILD_NUMBER}
        //                 cp -r ../magicreport ./
        //                 ls -la ./magicreport
        //                 mv ./magicreport/client/dist ./magicreport/
        //                 rm -rf ./magicreport/client
        //                 mv ./magicreport/dist ./magicreport/client

        //                 docker build --no-cache -t registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}:$TAG -f Dockerfile-magicreport .
        //                 docker push registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}:$TAG

        //                 # 保存TAG变量，供后续阶段使用
        //                 echo $TAG > ${var_file_path}/TAG
        //                 echo "registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}:$TAG" > ${var_file_path}/IMAGE
        //             '''
        //         }
        //     }
        // }

        // stage('Deploy') {
        //     agent {
        //         label 'main'
        //     }
        //     // when {
        //     //     // 添加条件判断，例如：当SKIP_SET_NAME参数不为true时执行
        //     //     // 您可以根据需要修改这个条件
        //     //     expression { return params.SKIP_SET_NAME != true }
        //     // }
        //     steps {
        //         sh '''

        //             # 读取TAG和IMAGE，如果不存在则使用默认值
        //             TAG=$(cat ./tmpdata/${BUILD_NUMBER}/TAG 2>/dev/null || echo "latest")
        //             IMAGE=$(cat ./tmpdata/${BUILD_NUMBER}/IMAGE 2>/dev/null || echo "latest")

        //             # 处理ROOTPATH参数
        //             ROOTPATH_PARAM="${ROOTPATH}"
        //             if [ -n "${ROOTPATH_PARAM}" ]; then
        //                 # 检查是否以/结尾，如果不是则添加
        //                 case "${ROOTPATH_PARAM}" in
        //                     */) ;;  # 已经以/结尾，不做任何操作
        //                     *) ROOTPATH_PARAM="${ROOTPATH_PARAM}/" ;;  # 添加/
        //                 esac
        //             else
        //                 ROOTPATH_PARAM=""
        //             fi

        //             # 替换DEPLOYENV中的点为下划线
        //             DEPLOYENV_CLEAN=$(echo "${DEPLOYENV}" | tr '.' '_')

        //             # 组合最终路径
        //             FINAL_PATH="${ROOTPATH_PARAM}${DEPLOYENV_CLEAN}"

        //             # 保存到文件供后续使用
        //             echo "${FINAL_PATH}" > ./tmpdata/${BUILD_NUMBER}/ROOTPATH
        //             echo "Using ROOTPATH: ${FINAL_PATH}"

        //             # 读取并显示提交哈希（已在之前的阶段保存）
        //             COMMIT_HASH=$(cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_magicreport 2>/dev/null || echo "未找到提交哈希")
        //             echo "使用的提交哈希: $COMMIT_HASH"

        //         '''

        //         script {
        //             // 读取生成的变量，供后续Groovy代码使用
        //             def TAG = sh(script: 'cat ./tmpdata/${BUILD_NUMBER}/TAG', returnStdout: true).trim()
        //             def IMAGE = sh(script: 'cat ./tmpdata/${BUILD_NUMBER}/IMAGE', returnStdout: true).trim()
        //             def ROOTPATH = sh(script: 'cat ./tmpdata/${BUILD_NUMBER}/ROOTPATH', returnStdout: true).trim()

        //             // 读取提交哈希
        //             def COMMIT_HASH_VALUE = sh(script: 'cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_magicreport', returnStdout: true).trim()
        //             echo "读取到的提交哈希: ${COMMIT_HASH_VALUE}"

        //             // 设置文件路径和环境变量
        //             env.COMMIT_HASH_magicreport = COMMIT_HASH_VALUE

        //             sh '''

        //                 if [ "${OFFLINE}" = "false" ]; then
        //                     set +ex
        //                     DEPLOY_SERVER_domain=`cat "./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_domain"`
        //                     DEPLOY_SERVER_port=`cat "./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_port"`
        //                     DEPLOY_SERVER_ip=`cat "./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip"`
        //                     DEPLOY_type=`cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_type`
        //                     DEPLOY_SERVER_ip_origin=`cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip_origin`
        //                     DEPLOY_HASH_magicreport=`cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_magicreport`
        //                     ROOTPATH=`cat ./tmpdata/${BUILD_NUMBER}/ROOTPATH`
        //                     TAG=`cat ./tmpdata/${BUILD_NUMBER}/TAG`
        //                     IMAGE=`cat ./tmpdata/${BUILD_NUMBER}/IMAGE`

        //                     echo $DEPLOY_SERVER_domain
        //                     echo $DEPLOY_SERVER_port
        //                     echo $DEPLOY_SERVER_ip
        //                     echo $DEPLOY_type
        //                     echo $DEPLOY_SERVER_ip_origin
        //                     echo $ROOTPATH
        //                     echo $TAG
        //                     echo $IMAGE

        //                     rm -f ./docker-compose-magicreport-case.yml
        //                     cp ./docker-compose-magicreport.yml ./docker-compose-magicreport-case.yml
        //                     sed -i "s|\\\${IMAGE}|${IMAGE}|g" ./docker-compose-magicreport-case.yml
        //                     sed -i "s|\\\${ROOTPATH}|${ROOTPATH}|g" ./docker-compose-magicreport-case.yml
        //                     sed -i "s|\\\${PORT}|${DEPLOY_SERVER_port}|g" ./docker-compose-magicreport-case.yml

        //                     ssh-keygen -f "${HOME}/.ssh/known_hosts" -R "${DEPLOY_SERVER_ip}" || true

        //                     ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} "mkdir -p ${ROOTPATH}/pm2logs; mkdir -p ${ROOTPATH}/server/uploads"

        //                     scp -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" ./docker-compose-magicreport-case.yml root@${DEPLOY_SERVER_ip}:${ROOTPATH}/docker-compose.yml

        //                     ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} "docker run --rm ${IMAGE} /bin/bash -c 'cd /var/www/server && npm run migration:run'"

        //                     ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} "cd ${ROOTPATH} && docker compose down && docker compose up -d"

        //                     echo $DEPLOY_SERVER_ip | grep '192.168.31.'
        //                     if [ $? -eq 0 ]; then
        //                         # 部署转发配置
        //                         if [ $DEPLOY_type = 'CREATE' -o $DEPLOY_type = 'CREATE_DELETE' ]; then
        //                             ngtpl_file='./servers/nginx.conf.tpl'
        //                             sed -i "s/{{ip}}/$DEPLOY_SERVER_ip/g" $ngtpl_file
        //                             sed -i "s/{{port}}/$DEPLOY_SERVER_port/g" $ngtpl_file
        //                             sed -i "s/{{domain}}/$DEPLOY_SERVER_domain/g" $ngtpl_file

        //                             cat $ngtpl_file
        //                             mv ./servers/nginx.conf.tpl "./servers/${DEPLOY_SERVER_domain}.conf"

        //                             ssh-keygen -f "${HOME}/.ssh/known_hosts" -R "**************" || true

        //                             scp -o StrictHostKeyChecking=no -i "${HOME}/.ssh/**************.pem" ./servers/${DEPLOY_SERVER_domain}.conf root@**************:/etc/nginx/conf.d/
        //                             ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/**************.pem" root@************** "nginx -t"
        //                             if [ $? -eq 0 ]; then
        //                             # 重启服务
        //                             ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/**************.pem" root@************** "nginx -s reload"
        //                                 echo "====== 重启转发服务 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
        //                             else
        //                                 echo "====== FAIL: 转发服务配置存在问题!!! ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
        //                             fi
        //                         fi

        //                         # CREATE_DELETE, 清理服务, docker compose down
        //                         set +ex
        //                         if [ $DEPLOY_type = 'CREATE_DELETE' ]; then
        //                             # 处理原始服务器的SSH主机密钥问题
        //                             ssh-keygen -f "${HOME}/.ssh/known_hosts" -R "${DEPLOY_SERVER_ip_origin}" || true

        //                             ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip_origin}.pem" root@${DEPLOY_SERVER_ip_origin} "cd /root/app/${DEPLOYENV_TO_DEPLOYDIR} && docker compose down"
        //                             if [ $? -eq 0 ]; then
        //                                 echo "====== 清理服务完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
        //                             else
        //                                 echo "====== 清理服务失败 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
        //                             fi
        //                         fi

        //                     fi

        //                     # 清理历史记录
        //                     sed -i "/$DEPLOY_SERVER_domain/d" "$DEPLOY_CACHE_file"

        //                     # 写入 deploy-records.data
        //                     # **************  FILE_FREEZE  cicada.bpmax.cn  2024-09-28T14:34:18CST
        //                     echo "$DEPLOY_SERVER_ip  $DEPLOY_SERVER_port  $DEPLOY_SERVER_domain  $(TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S%Z")  magicreport::$DEPLOY_HASH_magicreport" >> "$DEPLOY_CACHE_file"

        //                     cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary

        //                 else

        //                     # 清理历史记录
        //                     sed -i "/$DEPLOYENV/d" "$DEPLOY_CACHE_file"

        //                     # 写入打包记录
        //                     echo "OFFLINE  FILE_FREEZE  $DEPLOYENV  $(TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S%Z")  magicreport::$DEPLOY_HASH_magicreport" >> "$DEPLOY_CACHE_file"

        //                     # 人工到目标主机初始化
        //                     echo "mkdir -p /user/app/${DEPLOYENV_TO_DEPLOYDIR} && touch docker-compose.yml"
        //                     cat ./docker-compose-magicreport-case.yml

        //                 fi

        //             '''


        //         }
        //     }
        // }

    }

}
