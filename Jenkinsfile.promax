pipeline {
    agent {
        label 'main'
    }
    
    parameters {
        booleanParam(
            name: 'TESTING',
            defaultValue: false,
            description: '测试跳过环节'
        )
        booleanParam(
            name: 'FORCE_REBUILD_RUNTIME',
            defaultValue: false,
            description: '强制重建基础镜像 promax-runtime'
        )
        string(
            name: 'DEPLOY_BRANCH',
            defaultValue: 'master',
            description: '部署分支'
        )
        string(
            name: 'IMAGE_TAG',
            defaultValue: '',
            description: '镜像标签（留空则使用BUILD_NUMBER）'
        )
        choice(
            name: 'SERVER_ALIAS',
            choices: ['nil','dev005','dev006'],
            description: '服务器别名'
        )
        choice(
            name: 'DEPLOYENV',
            choices: ['promaxtest.yitaiyitai.com'],
            description: '部署环境'
        )
    }
    
    environment {
        // 基础配置 - 将从envs/.env_${params.DEPLOYENV}文件中加载更多变量
        BUILD_TIMESTAMP = sh(script: "date '+%Y%m%d_%H%M%S'", returnStdout: true).trim()
        // 定义缓存文件路径，确保在所有阶段都可用
        SERVERS_CACHE_file = "./servers/server_alias.data"
        DEPLOY_CACHE_file = "/var/jenkins_home/deploy-records.data"
    }
    
    stages {
        stage('读取缓存信息') {
            steps {
                sh '''
                echo "========== 开始调试信息 =========="
                echo "SERVER_ALIAS: ${SERVER_ALIAS}"
                echo "DEPLOYENV: ${DEPLOYENV}"
                echo "BUILD_NUMBER: ${BUILD_NUMBER}"

                # 调试当前工作目录和文件路径
                echo "当前工作目录: $(pwd)"
                echo "用户信息: $(whoami)"
                echo "环境变量 SERVERS_CACHE_file: ${SERVERS_CACHE_file}"
                echo "环境变量 DEPLOY_CACHE_file: ${DEPLOY_CACHE_file}"

                echo "列出当前目录内容:"
                ls -la

                echo "检查 servers 目录:"
                if [ -d "servers" ]; then
                    echo "servers 目录存在，内容如下:"
                    ls -la servers/
                else
                    echo "servers 目录不存在"
                fi

                # 尝试读取文件
                echo "检查服务器别名文件: ${SERVERS_CACHE_file}"
                if [ -f "${SERVERS_CACHE_file}" ]; then
                    echo "✅ 服务器别名文件存在"
                    echo "文件权限: $(ls -l ${SERVERS_CACHE_file})"
                    echo "文件内容:"
                    cat ${SERVERS_CACHE_file}
                else
                    echo "❌ 错误: 服务器别名文件不存在: ${SERVERS_CACHE_file}"
                    echo "尝试查找可能的位置:"
                    find . -name "server_alias.data" -type f 2>/dev/null || echo "未找到 server_alias.data 文件"
                fi

                echo "检查部署记录文件: ${DEPLOY_CACHE_file}"
                if [ -f "${DEPLOY_CACHE_file}" ]; then
                    echo "✅ 部署记录文件存在"
                    echo "文件权限: $(ls -l ${DEPLOY_CACHE_file})"
                    echo "文件内容:"
                    cat ${DEPLOY_CACHE_file}
                else
                    echo "❌ 部署记录文件不存在: ${DEPLOY_CACHE_file}"
                fi
                echo "========== 调试信息结束 =========="

                var_file_path=./tmpdata/${BUILD_NUMBER}/
                rm -rf "$var_file_path"
                mkdir -p "$var_file_path"
                chmod 777 -R "$var_file_path"
                echo "======BUILD_NUMBER: ${BUILD_NUMBER} ======"
                touch "${var_file_path}/DEPLOY_summary"

                # 初始化部署相关变量
                DEPLOY_SERVER_ip=""
                DEPLOY_SERVER_port=""
                DEPLOY_SERVER_domain=""
                DEPLOY_type="CREATE"
                DEPLOY_SERVER_ip_origin=""

                # 检查部署记录文件是否存在
                if [ -f "${DEPLOY_CACHE_file}" ]; then
                    echo "部署记录文件存在"
                    deploy_record="$(cat "$DEPLOY_CACHE_file" | grep "$DEPLOYENV" | tail -1)"

                    if [ -z "$deploy_record" ]; then
                        echo "====== deploy record is nil ======"

                        if [ "$SERVER_ALIAS" = 'nil' ]; then
                            echo "====== SERVER_ALIAS is nil, deploy fail!!! ======"
                            exit 1
                        else
                            echo "====== SERVER_ALIAS: $SERVER_ALIAS ======"
                            echo "尝试从文件中查找 SERVER_ALIAS..."
                            echo "执行命令: cat $SERVERS_CACHE_file | grep \"$SERVER_ALIAS\""

                            # 先检查文件是否可读
                            if [ -r "$SERVERS_CACHE_file" ]; then
                                echo "文件可读，查找 $SERVER_ALIAS..."
                                grep_result=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" || echo "")
                                echo "grep 结果: '$grep_result'"

                                if [ -n "$grep_result" ]; then
                                    DEPLOY_SERVER_ip=$(echo "$grep_result" | awk -F"=" '{print $2}')
                                    echo "提取的 IP: '$DEPLOY_SERVER_ip'"
                                else
                                    echo "未找到匹配的 SERVER_ALIAS: $SERVER_ALIAS"
                                fi
                            else
                                echo "文件不可读: $SERVERS_CACHE_file"
                            fi

                            echo "====== DEPLOY_SERVER_ip: $DEPLOY_SERVER_ip ======"
                            DEPLOY_type="CREATE"
                            DEPLOY_SERVER_domain=$DEPLOYENV
                        fi
                    else
                        echo "====== deploy record exist: $deploy_record ======"
                        DEPLOY_SERVER_ip=$(echo $deploy_record | awk -F' ' '{print $1}')
                        DEPLOY_SERVER_port=$(echo $deploy_record | awk -F' ' '{print $2}')
                        DEPLOY_SERVER_domain=${DEPLOYENV}

                        if [ "$SERVER_ALIAS" = 'nil' ]; then
                            echo "====== SERVER_ALIAS is nil, deploy update!!! ======"
                            DEPLOY_type="UPDATE"
                        else
                            echo "====== SERVER_ALIAS exist: $SERVER_ALIAS, deploy create and delete!!! ======"
                            DEPLOY_SERVER_ip_origin=$DEPLOY_SERVER_ip

                            echo "尝试从文件中查找新的 SERVER_ALIAS..."
                            echo "执行命令: cat $SERVERS_CACHE_file | grep \"$SERVER_ALIAS\""

                            # 先检查文件是否可读
                            if [ -r "$SERVERS_CACHE_file" ]; then
                                echo "文件可读，查找 $SERVER_ALIAS..."
                                grep_result=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" || echo "")
                                echo "grep 结果: '$grep_result'"

                                if [ -n "$grep_result" ]; then
                                    DEPLOY_SERVER_ip=$(echo "$grep_result" | awk -F"=" '{print $2}')
                                    echo "提取的新 IP: '$DEPLOY_SERVER_ip'"
                                else
                                    echo "未找到匹配的 SERVER_ALIAS: $SERVER_ALIAS"
                                fi
                            else
                                echo "文件不可读: $SERVERS_CACHE_file"
                            fi

                            echo "====== DEPLOY_SERVER_ip_origin: $DEPLOY_SERVER_ip_origin ======"
                            echo "====== DEPLOY_SERVER_ip: $DEPLOY_SERVER_ip ======"
                            DEPLOY_type="CREATE_DELETE"

                            if [ "$DEPLOY_SERVER_ip_origin" = "$DEPLOY_SERVER_ip" ]; then
                                echo "====== IP addresses are the same ======"
                                DEPLOY_type="UPDATE"
                            fi
                        fi
                    fi
                else
                    echo "部署记录文件不存在"
                    if [ "$SERVER_ALIAS" = 'nil' ]; then
                        echo "====== SERVER_ALIAS is nil, deploy fail!!! ======"
                        exit 1
                    else
                        echo "====== SERVER_ALIAS: $SERVER_ALIAS ======"
                        echo "尝试从文件中查找 SERVER_ALIAS (部署记录不存在的情况)..."
                        echo "执行命令: cat $SERVERS_CACHE_file | grep \"$SERVER_ALIAS\""

                        # 先检查文件是否可读
                        if [ -r "$SERVERS_CACHE_file" ]; then
                            echo "文件可读，查找 $SERVER_ALIAS..."
                            grep_result=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" || echo "")
                            echo "grep 结果: '$grep_result'"

                            if [ -n "$grep_result" ]; then
                                DEPLOY_SERVER_ip=$(echo "$grep_result" | awk -F"=" '{print $2}')
                                echo "提取的 IP: '$DEPLOY_SERVER_ip'"
                            else
                                echo "未找到匹配的 SERVER_ALIAS: $SERVER_ALIAS"
                            fi
                        else
                            echo "文件不可读: $SERVERS_CACHE_file"
                        fi

                        echo "====== DEPLOY_SERVER_ip: $DEPLOY_SERVER_ip ======"
                        DEPLOY_type="CREATE"
                        DEPLOY_SERVER_domain=$DEPLOYENV
                    fi
                fi

                echo "====== check important vars: ip $DEPLOY_SERVER_ip, port $DEPLOY_SERVER_port, domain $DEPLOY_SERVER_domain, ip_origin $DEPLOY_SERVER_ip_origin, type $DEPLOY_type ======"

                # 保存变量到临时文件的函数
                create_var_file() {
                    filename="${1}${2}"
                    touch "$filename"
                    if [ -z "$3" ]; then
                        echo "WARNING: val is nil for $2, creating empty file"
                        echo "" > "$filename"
                    else
                        echo "保存变量 $2 = '$3' 到文件 $filename"
                        echo $3 > "$filename"
                    fi
                }

                create_var_file $var_file_path DEPLOY_type $DEPLOY_type
                create_var_file $var_file_path DEPLOY_SERVER_ip $DEPLOY_SERVER_ip
                create_var_file $var_file_path DEPLOY_SERVER_port $DEPLOY_SERVER_port
                create_var_file $var_file_path DEPLOY_SERVER_domain $DEPLOY_SERVER_domain
                create_var_file $var_file_path DEPLOY_SERVER_ip_origin $DEPLOY_SERVER_ip_origin

                # 验证文件是否成功创建
                echo "========== 验证文件创建状态 =========="
                echo "检查临时文件目录: $var_file_path"
                ls -la "$var_file_path"

                echo "验证关键文件内容:"
                for file in DEPLOY_type DEPLOY_SERVER_ip DEPLOY_SERVER_port DEPLOY_SERVER_domain DEPLOY_SERVER_ip_origin; do
                    filepath="${var_file_path}${file}"
                    if [ -f "$filepath" ]; then
                        echo "✅ $file: $(cat $filepath)"
                    else
                        echo "❌ $file: 文件不存在"
                    fi
                done
                echo "========== 文件验证完成 =========="

                echo "====== 缓存信息读取完成 ======" >> "${var_file_path}/DEPLOY_summary"
                '''
            }
        }

        stage('加载环境变量') {
            steps {
                script {
                    echo "📋 加载环境变量配置"

                    // 检查环境变量文件是否存在
                    def envFileName = "envs/.env_${params.DEPLOYENV}"
                    if (!fileExists(envFileName)) {
                        error "❌ ${envFileName} 文件不存在，请确保文件存在"
                    }

                    // 加载环境变量文件中的环境变量
                    def envVars = readFile(envFileName)
                        .split('\n')
                        .findAll { line ->
                            line.trim() && !line.trim().startsWith('#') && line.contains('=')
                        }
                        .collectEntries { line ->
                            def parts = line.split('=', 2)
                            [(parts[0].trim()): parts[1].trim()]
                        }

                    // 设置环境变量
                    envVars.each { key, value ->
                        env."${key}" = value
                    }

                    // 设置构建相关的动态变量
                    env.IMAGE_TAG_FINAL = params.IMAGE_TAG ?: BUILD_NUMBER
                    env.FULL_IMAGE_NAME = "${env.REGISTRY_URL}/${env.IMAGE_NAME}:${env.IMAGE_TAG_FINAL}"

                    echo "✅ 环境变量加载完成"
                    echo "已加载 ${envVars.size()} 个环境变量"
                }
            }
        }
        
        stage('准备阶段') {
            steps {
                script {
                    echo "🚀 开始 ProMax 部署流程"
                    echo "================================"
                    echo "构建编号: ${BUILD_NUMBER}"
                    echo "构建时间: ${BUILD_TIMESTAMP}"
                    echo "提交哈希: ${env.COMMIT_HASH}"
                    echo "部署分支: ${params.DEPLOY_BRANCH}"
                    echo "镜像名称: ${env.FULL_IMAGE_NAME}"
                    echo "容器名称: ${env.CONTAINER_NAME}"
                    echo "镜像仓库: ${env.REGISTRY_URL}"
                    echo "================================"
                    
                    // 清理并重建临时目录，参照 magicreport 的模式
                    sh """
                        var_file_path=./tmpdata/${BUILD_NUMBER}
                        rm -rf "\$var_file_path"
                        mkdir -p "\$var_file_path"
                        chmod 777 -R "\$var_file_path"
                        
                        # 创建 DEPLOY_summary 文件用于记录部署日志
                        touch "\${var_file_path}/DEPLOY_summary"
                        
                        # 保存已知的构建变量到独立文件
                        echo "${BUILD_NUMBER}" > "\${var_file_path}/BUILD_NUMBER"
                        echo "${BUILD_URL}" > "\${var_file_path}/BUILD_URL"
                        echo "${JOB_NAME}" > "\${var_file_path}/JOB_NAME"
                        echo "${params.DEPLOY_BRANCH}" > "\${var_file_path}/BRANCH_NAME"
                        echo "${params.DEPLOYENV}" > "\${var_file_path}/DEPLOYENV"
                        echo "${params.SERVER_ALIAS}" > "\${var_file_path}/SERVER_ALIAS"
                        echo "\$(TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S%Z")" > "\${var_file_path}/BUILD_TIMESTAMP"
                        
                        # 记录准备阶段完成
                        echo "====== 准备阶段完成 ======" >> "\${var_file_path}/DEPLOY_summary"
                    """
                }
            }
        }
        
        stage('代码检出') {
            steps {
                script {
                    echo "📥 检出代码分支: ${params.DEPLOY_BRANCH}"
                    
                    // 检出指定分支
                    checkout([
                        $class: 'GitSCM', 
                        branches: [[name: "refs/heads/${params.DEPLOY_BRANCH}"]], 
                            userRemoteConfigs: [
                            [url: "${env.GIT_REPO_URL}", credentialsId: "${env.GIT_CREDENTIALS_ID}"]
                        ]
                    ])
                    
                    // 配置 git 安全目录并获取提交哈希，参照 magicreport 的模式
                    def JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                    sh "git config --global --add safe.directory ${JOB_PWD}"
                    
                    env.COMMIT_HASH = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                    echo "📝 提交哈希: ${env.COMMIT_HASH}"
                    echo "工作目录: ${JOB_PWD}"
                    
                    // 保存 COMMIT_HASH 到 tmpdata 目录
                    sh """
                        echo "${env.COMMIT_HASH}" > ./tmpdata/${BUILD_NUMBER}/COMMIT_HASH
                        echo "保存提交哈希到 ./tmpdata/${BUILD_NUMBER}/COMMIT_HASH"
                        
                        # 记录代码检出完成
                        echo "====== 代码检出完成，COMMIT_HASH: ${env.COMMIT_HASH} ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                }
            }
        }
        
        stage('构建基础镜像') {
            when {
                expression { params.FORCE_REBUILD_RUNTIME == true }
            }
            steps {
                script {
                    // 构建基础镜像
                    env.BASE_IMAGE_TAG = "${env.BUILD_TIMESTAMP}"
                    env.BASE_IMAGE_NAME = "${env.REGISTRY_URL}/${env.IMAGE_NAME}-base:${env.BASE_IMAGE_TAG}"
                    
                    echo "🏗️ 构建基础镜像: ${env.BASE_IMAGE_NAME}"
                    sh "docker build -f docker/Dockerfile.runtime -t ${env.BASE_IMAGE_NAME} ."
                    
                    echo "📤 推送基础镜像到仓库"
                    sh "docker push ${env.BASE_IMAGE_NAME}"
                    
                    echo "✅ 基础镜像构建和推送完成"
                }
            }
        }
        
        stage('获取最新基础镜像标签') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    // 获取最新的基础镜像标签
                    try {
                        def latestBaseTag = sh(
                            script: "docker images --format 'table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}' | grep '${env.REGISTRY_URL}/${env.IMAGE_NAME}-base' | head -1 | awk '{print \$1}' | cut -d':' -f2",
                            returnStdout: true
                        ).trim()
                        
                        if (latestBaseTag) {
                            env.LATEST_BASE_IMAGE_TAG = latestBaseTag
                        } else {
                            // 如果没有找到本地基础镜像，使用默认标签
                            env.LATEST_BASE_IMAGE_TAG = "latest"
                        }
                    } catch (Exception e) {
                        echo "⚠️ 获取基础镜像标签失败，使用默认标签: ${e.message}"
                        env.LATEST_BASE_IMAGE_TAG = "latest"
                    }
                    
                    env.LATEST_BASE_IMAGE_NAME = "${env.REGISTRY_URL}/${env.IMAGE_NAME}-base:${env.LATEST_BASE_IMAGE_TAG}"
                    echo "📋 使用基础镜像: ${env.LATEST_BASE_IMAGE_NAME}"
                }
            }
        }
        
        stage('构建应用镜像') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    // 动态更新Dockerfile中的FROM镜像
                    sh '''
                        # 备份原始Dockerfile
                        cp docker/Dockerfile.promax.production docker/Dockerfile.promax.production.bak
                        
                        # 更新FROM镜像为最新的基础镜像
                        sed -i.tmp "s|FROM registry.yitaiyitai.com/promax/promax-base:v1|FROM ${env.LATEST_BASE_IMAGE_NAME}|g" docker/Dockerfile.promax.production
                        
                        echo "📝 已更新Dockerfile.promax.production中的基础镜像为: ${env.LATEST_BASE_IMAGE_NAME}"
                    '''
                    
                    // 将构建时需要的环境变量文件复制到构建上下文中
                    def envFileName = "envs/.env_${params.DEPLOYENV}"
                    if (!fileExists(envFileName)) {
                        error "❌ 构建环境变量文件 ${envFileName} 不存在！"
                    }
                    
                    try {
                        sh "cp ${envFileName} ./.env"
                        echo "🐳 构建应用镜像: ${env.FULL_IMAGE_NAME}"
                        sh "docker build -f docker/Dockerfile.promax.production -t ${env.FULL_IMAGE_NAME} ."
                    } finally {
                        // 清理临时的环境变量文件
                        sh "rm ./.env"
                    }
                    
                    echo "📤 推送应用镜像到仓库"
                    sh "docker push ${env.FULL_IMAGE_NAME}"
                    
                    // 恢复原始Dockerfile
                    sh "mv docker/Dockerfile.promax.production.bak docker/Dockerfile.promax.production"
                    
                    echo "✅ 应用镜像构建和推送完成"
                }
            }
        }
        
        stage('生成最终部署配置') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "🔄 动态生成最终的 docker-compose.yml"

                    // 1. 读取 .env 文件
                    def envFileName = "envs/.env_${params.DEPLOYENV}"
                    if (!fileExists(envFileName)) {
                        error "❌ 环境变量文件 ${envFileName} 不存在！"
                    }
                    def envContent = readFile(envFileName)
                    
                    // 2. 格式化环境变量为 YAML map
                    def formattedEnvs = envContent.split('\n').findAll { line ->
                        line.trim() && !line.trim().startsWith('#') && line.contains('=')
                    }.collect { line ->
                        def parts = line.split('=', 2)
                        def key = parts[0].trim()
                        def value = parts[1].trim()
                        // 6 spaces indentation for key
                        "      ${key}: '${value.replace("'", "''")}'"
                    }.join('\n')

                    // 3. 读取 docker-compose 模板
                    def composeTemplate = readFile('docker-compose-promax.yml')

                    // 4. 替换镜像地址占位符
                    def composeWithImage = composeTemplate.replace('${IMAGE}', "${env.FULL_IMAGE_NAME}")

                    // 5. 替换环境变量占位符
                    def finalComposeContent = composeWithImage.replace(
                        '# DYNAMIC_ENVIRONMENT_VARIABLES_PLACEHOLDER',
                        formattedEnvs
                    )

                    // 6. 写入最终的 docker-compose.yml 文件
                    writeFile file: 'docker-compose.yml', text: finalComposeContent

                    echo "✅ 最终 docker-compose.yml 生成完毕"
                    echo "🔍 生成的配置内容预览:"
                    sh 'cat docker-compose.yml'
                }
            }
        }
        
        stage('分配端口') {
            steps {
                sh '''
                echo "🔍 分配动态端口..."

                var_file_path=./tmpdata/${BUILD_NUMBER}/

                # 从临时文件读取部署变量
                echo "========== 开始读取临时文件 =========="
                echo "临时文件目录: $var_file_path"
                echo "检查临时文件目录是否存在:"
                if [ -d "$var_file_path" ]; then
                    echo "✅ 临时文件目录存在"
                    echo "目录内容:"
                    ls -la "$var_file_path"
                else
                    echo "❌ 临时文件目录不存在: $var_file_path"
                    exit 1
                fi

                echo "尝试读取各个变量文件:"

                # 安全地读取每个文件
                if [ -f "${var_file_path}DEPLOY_type" ]; then
                    DEPLOY_type=`cat ${var_file_path}DEPLOY_type`
                    echo "✅ DEPLOY_type: $DEPLOY_type"
                else
                    echo "❌ DEPLOY_type 文件不存在"
                    DEPLOY_type=""
                fi

                if [ -f "${var_file_path}DEPLOY_SERVER_ip" ]; then
                    DEPLOY_SERVER_ip=`cat ${var_file_path}DEPLOY_SERVER_ip`
                    echo "✅ DEPLOY_SERVER_ip: $DEPLOY_SERVER_ip"
                else
                    echo "❌ DEPLOY_SERVER_ip 文件不存在"
                    DEPLOY_SERVER_ip=""
                fi

                if [ -f "${var_file_path}DEPLOY_SERVER_port" ]; then
                    DEPLOY_SERVER_port=`cat ${var_file_path}DEPLOY_SERVER_port`
                    echo "✅ DEPLOY_SERVER_port: $DEPLOY_SERVER_port"
                else
                    echo "❌ DEPLOY_SERVER_port 文件不存在"
                    DEPLOY_SERVER_port=""
                fi

                if [ -f "${var_file_path}DEPLOY_SERVER_domain" ]; then
                    DEPLOY_SERVER_domain=`cat ${var_file_path}DEPLOY_SERVER_domain`
                    echo "✅ DEPLOY_SERVER_domain: $DEPLOY_SERVER_domain"
                else
                    echo "❌ DEPLOY_SERVER_domain 文件不存在"
                    DEPLOY_SERVER_domain=""
                fi

                if [ -f "${var_file_path}DEPLOY_SERVER_ip_origin" ]; then
                    DEPLOY_SERVER_ip_origin=`cat ${var_file_path}DEPLOY_SERVER_ip_origin`
                    echo "✅ DEPLOY_SERVER_ip_origin: $DEPLOY_SERVER_ip_origin"
                else
                    echo "❌ DEPLOY_SERVER_ip_origin 文件不存在"
                    DEPLOY_SERVER_ip_origin=""
                fi

                echo "========== 文件读取完成 =========="
                echo "读取到的变量: type=$DEPLOY_type, ip=$DEPLOY_SERVER_ip, port=$DEPLOY_SERVER_port"

                # 定义端口探测函数 (参考MEP的逻辑，但使用35000-36000范围)
                probe_port() {
                    for i in $(seq 35000 36000); do
                        echo "=== probe $1 $i ==="
                        result=$(timeout 1 telnet $1 $i 2>&1)
                        r=$(echo $result | grep -v "Escape character")
                        if [ -n "$r" ]; then
                            DEPLOY_SERVER_port=$i
                            break;
                        fi
                    done
                }

                # 根据部署类型决定端口分配策略
                if [ "$DEPLOY_type" = "UPDATE" ] && [ -n "$DEPLOY_SERVER_port" ] && [ "$DEPLOY_SERVER_port" != "FILE_FREEZE" ]; then
                    echo "====== UPDATE类型，重用已有端口: $DEPLOY_SERVER_port ======"
                    PORT1=$(echo $DEPLOY_SERVER_port | cut -d',' -f1)
                    PORT2=$(echo $DEPLOY_SERVER_port | cut -d',' -f2)
                    PORT3=$(echo $DEPLOY_SERVER_port | cut -d',' -f3)

                    # 如果端口信息不完整，使用第一个端口作为基础
                    if [ -z "$PORT1" ]; then
                        PORT1=$DEPLOY_SERVER_port
                    fi
                    if [ -z "$PORT2" ]; then
                        PORT2=$((PORT1 + 1))
                    fi
                    if [ -z "$PORT3" ]; then
                        PORT3=$((PORT1 + 2))
                    fi

                elif [ "$DEPLOY_type" = "CREATE" ] || [ "$DEPLOY_type" = "CREATE_DELETE" ]; then
                    echo "====== CREATE类型，分配新端口 ======"

                    # 检查是否为内网IP (192.168.31.x)
                    echo $DEPLOY_SERVER_ip | grep '192.168.31.'
                    if [ $? -eq 0 ]; then
                        # 内网服务器，探测端口
                        probe_port $DEPLOY_SERVER_ip
                        PORT1=$DEPLOY_SERVER_port
                        PORT2=$((PORT1 + 1))
                        PORT3=$((PORT1 + 2))
                        echo "内网服务器，分配端口: PORT1=$PORT1, PORT2=$PORT2, PORT3=$PORT3"
                    else
                        # 外网服务器，使用FILE_FREEZE标记
                        DEPLOY_SERVER_port="FILE_FREEZE"
                        PORT1="35000"
                        PORT2="35001"
                        PORT3="35002"
                        echo "外网服务器，使用默认端口: PORT1=$PORT1, PORT2=$PORT2, PORT3=$PORT3"
                    fi
                else
                    # 其他情况，使用默认端口
                    PORT1="35000"
                    PORT2="35001"
                    PORT3="35002"
                    echo "使用默认端口: PORT1=$PORT1, PORT2=$PORT2, PORT3=$PORT3"
                fi

                # 更新DEPLOY_SERVER_port为逗号分隔的端口列表
                if [ "$DEPLOY_SERVER_port" != "FILE_FREEZE" ]; then
                    DEPLOY_SERVER_port="${PORT1},${PORT2},${PORT3}"
                fi

                echo "✅ 端口分配完成:"
                echo "   主应用端口: $PORT1"
                echo "   API端口: $PORT2"
                echo "   Python挖掘服务端口: $PORT3"
                echo "   DEPLOY_SERVER_port: $DEPLOY_SERVER_port"

                # 保存端口信息到临时文件（使用之前定义的 create_var_file 函数）
                # 重新定义 create_var_file 函数以确保一致性
                create_var_file() {
                    filename="${1}${2}"
                    touch "$filename"
                    if [ -z "$3" ]; then
                        echo "WARNING: val is nil for $2, creating empty file"
                        echo "" > "$filename"
                    else
                        echo "保存变量 $2 = '$3' 到文件 $filename"
                        echo $3 > "$filename"
                    fi
                }

                create_var_file $var_file_path PORT1 $PORT1
                create_var_file $var_file_path PORT2 $PORT2
                create_var_file $var_file_path PORT3 $PORT3
                create_var_file $var_file_path DEPLOY_SERVER_port $DEPLOY_SERVER_port

                # 验证端口文件是否成功创建
                echo "========== 验证端口文件创建状态 =========="
                echo "检查临时文件目录: $var_file_path"
                ls -la "$var_file_path"

                echo "验证端口文件内容:"
                for file in PORT1 PORT2 PORT3 DEPLOY_SERVER_port; do
                    filepath="${var_file_path}${file}"
                    if [ -f "$filepath" ]; then
                        echo "✅ $file: $(cat $filepath)"
                    else
                        echo "❌ $file: 文件不存在"
                    fi
                done
                echo "========== 端口文件验证完成 =========="

                echo "====== 端口分配完成: PORT1=$PORT1, PORT2=$PORT2, PORT3=$PORT3 ======" >> "${var_file_path}/DEPLOY_summary"
                '''
            }
        }
        
        stage('准备部署文件') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "📤 准备部署文件..."
                    
                    // 使用模板文件生成最终的docker-compose.yml
                    sh """
                        echo "生成docker-compose.yml文件..."
                        cp docker-compose-promax.yml docker-compose-generated.yml
                        
                        # 替换变量
                        sed -i 's/\${IMAGE}/${env.FULL_IMAGE_NAME}/g' docker-compose-generated.yml
                        sed -i 's/\${PORT1}/${env.PORT1}/g' docker-compose-generated.yml
                        sed -i 's/\${PORT2}/${env.PORT2}/g' docker-compose-generated.yml
                        sed -i 's/\${PORT3}/${env.PORT3}/g' docker-compose-generated.yml
                        
                        # 替换其他环境变量
                        sed -i "s|\${REMOTE_DEPLOY_DIR}|${env.REMOTE_DEPLOY_DIR}|g" docker-compose-generated.yml
                        sed -i "s|\${GIT_REPO_URL}|${env.GIT_REPO_URL}|g" docker-compose-generated.yml
                        sed -i 's/\${GIT_CREDENTIALS_ID}/${env.GIT_CREDENTIALS_ID}/g' docker-compose-generated.yml
                        sed -i "s|\${REGISTRY_URL}|${env.REGISTRY_URL}|g" docker-compose-generated.yml
                    """
                    
                    // 清除known_hosts记录，确保SSH连接安全
                    sh """
                        ssh-keygen -f "${HOME}/.ssh/known_hosts" -R "${env.DEPLOY_SERVER_ip}" || true
                        echo "====== 清除 known_hosts 记录完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 创建远程部署目录
                    sh """
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "mkdir -p ${env.REMOTE_DEPLOY_DIR}"
                        echo "====== 创建远程部署目录完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 同步生成的docker-compose.yml到远程服务器
                    sh """
                        echo "同步docker-compose.yml到远程服务器..."
                        scp -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" docker-compose-generated.yml root@${env.DEPLOY_SERVER_ip}:${env.REMOTE_DEPLOY_DIR}/docker-compose.yml
                        echo "====== 同步 docker-compose.yml 完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 同步其他部署相关文件
                    sh """
                        rsync -avz -e "ssh -o StrictHostKeyChecking=no -i ${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" \
                            config/ \
                            scripts/load-env.sh \
                            root@${env.DEPLOY_SERVER_ip}:${env.REMOTE_DEPLOY_DIR}/
                        echo "====== 同步部署相关文件完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 恢复原始docker-compose.yml
                    sh "mv docker-compose.yml.bak docker-compose.yml"
                    
                    echo "✅ 部署文件准备完成"
                }
            }
        }
        

        
        stage('部署服务') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                sh '''
                echo "🚀 开始部署服务..."

                var_file_path=./tmpdata/${BUILD_NUMBER}/

                # 从临时文件读取部署变量
                DEPLOY_SERVER_ip=`cat ${var_file_path}DEPLOY_SERVER_ip`

                echo "部署到服务器: $DEPLOY_SERVER_ip"

                # 拉取最新镜像
                echo "📥 拉取最新镜像..."
                ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} \
                "docker pull ${FULL_IMAGE_NAME}"
                echo "====== 拉取最新镜像完成 ======" >> ${var_file_path}/DEPLOY_summary

                # 停止现有服务
                echo "🛑 停止现有服务..."
                ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} \
                "cd ${REMOTE_DEPLOY_DIR} && docker-compose down || true"
                echo "====== 停止现有服务完成 ======" >> ${var_file_path}/DEPLOY_summary

                # 启动服务
                echo "🚀 启动服务..."
                ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} \
                "cd ${REMOTE_DEPLOY_DIR} && docker-compose up -d"
                echo "====== 启动服务完成 ======" >> ${var_file_path}/DEPLOY_summary
                '''
            }
        }
        
        stage('健康检查') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                sh '''
                echo "🔍 执行健康检查..."

                var_file_path=./tmpdata/${BUILD_NUMBER}/

                # 从临时文件读取部署变量
                DEPLOY_SERVER_ip=`cat ${var_file_path}DEPLOY_SERVER_ip`
                PORT1=`cat ${var_file_path}PORT1`

                echo "健康检查服务器: $DEPLOY_SERVER_ip, 端口: $PORT1"

                # 等待服务启动
                sleep 30

                # 检查docker-compose服务状态
                echo "检查服务状态..."
                ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} \
                "cd ${REMOTE_DEPLOY_DIR} && docker-compose ps"
                echo "====== 服务状态检查完成 ======" >> ${var_file_path}/DEPLOY_summary

                # 检查容器日志
                echo "检查容器日志..."
                ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} \
                "cd ${REMOTE_DEPLOY_DIR} && docker-compose logs --tail=20 ${CONTAINER_NAME} || echo '⚠️ 日志检查失败'"
                echo "====== 容器日志检查完成 ======" >> ${var_file_path}/DEPLOY_summary

                # 测试API端点
                echo "测试API端点..."
                ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} \
                "curl -f http://localhost:${PORT1}/api/health 2>/dev/null || echo '⚠️ API健康检查失败'"
                echo "====== API端点测试完成 ======" >> ${var_file_path}/DEPLOY_summary

                echo "✅ 健康检查完成"
                '''
            }
        }
        
        stage('更新部署记录') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                sh '''
                echo "📝 更新部署记录..."

                var_file_path=./tmpdata/${BUILD_NUMBER}/

                # 从临时文件读取部署变量
                DEPLOY_SERVER_ip=`cat ${var_file_path}DEPLOY_SERVER_ip`
                DEPLOY_SERVER_port=`cat ${var_file_path}DEPLOY_SERVER_port`
                DEPLOY_SERVER_domain=`cat ${var_file_path}DEPLOY_SERVER_domain`

                echo "更新部署记录: ip=$DEPLOY_SERVER_ip, port=$DEPLOY_SERVER_port, domain=$DEPLOY_SERVER_domain"

                # 确保全局部署记录文件存在且有正确权限
                sudo mkdir -p /var/jenkins_home

                if [ ! -f "${DEPLOY_CACHE_file}" ]; then
                    sudo touch "${DEPLOY_CACHE_file}"
                    sudo chmod 666 "${DEPLOY_CACHE_file}"
                    echo "创建全局部署记录文件: ${DEPLOY_CACHE_file}"
                fi

                sudo chmod 666 "${DEPLOY_CACHE_file}"

                # 清理历史记录
                temp_file=$(mktemp)
                grep -v "$DEPLOYENV" "${DEPLOY_CACHE_file}" > "$temp_file" || true
                mv "$temp_file" "${DEPLOY_CACHE_file}"
                chmod 666 "${DEPLOY_CACHE_file}"

                # 写入新的部署记录
                # 格式: IP PORT DOMAIN TIMESTAMP HASH
                timestamp=$(TZ='Asia/Shanghai' date '+%Y-%m-%dT%H:%M:%S%Z')
                deploy_record="$DEPLOY_SERVER_ip $DEPLOY_SERVER_port $DEPLOYENV $timestamp promax::${COMMIT_HASH}"

                # 使用文件锁确保原子性写入
                (
                    flock -x 200
                    echo "$deploy_record" >> "${DEPLOY_CACHE_file}"
                    chmod 666 "${DEPLOY_CACHE_file}"
                ) 200>"${DEPLOY_CACHE_file}.lock"

                # 清理锁文件
                rm -f "${DEPLOY_CACHE_file}.lock"

                echo "已更新部署记录: $deploy_record"
                echo "✅ 部署记录更新完成"
                '''
            }
        }

        stage('生成部署报告') {
            steps {
                sh '''
                echo "📋 生成部署报告..."

                var_file_path=./tmpdata/${BUILD_NUMBER}/

                # 从临时文件读取部署变量
                DEPLOY_SERVER_ip=`cat ${var_file_path}DEPLOY_SERVER_ip`
                DEPLOY_SERVER_port=`cat ${var_file_path}DEPLOY_SERVER_port`
                DEPLOY_type=`cat ${var_file_path}DEPLOY_type`
                PORT1=`cat ${var_file_path}PORT1`
                PORT2=`cat ${var_file_path}PORT2`
                PORT3=`cat ${var_file_path}PORT3`

                echo "生成部署报告，服务器: $DEPLOY_SERVER_ip"

                # 生成部署报告
                cat > ${var_file_path}deploy-report.txt << EOF
🎉 ProMax 部署完成报告
================================
构建编号: ${BUILD_NUMBER}
构建时间: ${BUILD_TIMESTAMP}
提交哈希: ${COMMIT_HASH}
部署分支: ${DEPLOY_BRANCH}
镜像名称: ${FULL_IMAGE_NAME}
目标服务器: root@${DEPLOY_SERVER_ip}
容器名称: ${CONTAINER_NAME}
部署类型: ${DEPLOY_type}

端口分配:
- 主应用端口: ${PORT1} (映射到容器内3100)
- API端口: ${PORT2} (映射到容器内3101)
- Python挖掘服务端口: ${PORT3} (映射到容器内8000)

访问地址:
- 主应用: http://${DEPLOY_SERVER_ip}:${PORT1}
- API接口: http://${DEPLOY_SERVER_ip}:${PORT2}/api
- API文档: http://${DEPLOY_SERVER_ip}:${PORT2}/api/docs
- 挖掘服务: http://${DEPLOY_SERVER_ip}:${PORT3}
- 挖掘服务文档: http://${DEPLOY_SERVER_ip}:${PORT3}/docs

管理命令:
- 查看服务状态: ssh root@${DEPLOY_SERVER_ip} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose ps'
- 查看服务日志: ssh root@${DEPLOY_SERVER_ip} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose logs -f'
- 重启服务: ssh root@${DEPLOY_SERVER_ip} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose restart'
- 停止服务: ssh root@${DEPLOY_SERVER_ip} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose down'
- 启动服务: ssh root@${DEPLOY_SERVER_ip} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose up -d'
- 进入容器: ssh root@${DEPLOY_SERVER_ip} 'cd ${REMOTE_DEPLOY_DIR} && docker-compose exec ${CONTAINER_NAME} bash'

部署目录: ${REMOTE_DEPLOY_DIR}
================================
EOF

                # 显示部署报告
                cat ${var_file_path}deploy-report.txt

                # 显示部署摘要
                echo ""
                echo "📋 部署摘要:"
                cat ${var_file_path}DEPLOY_summary || echo '无部署摘要信息'

                echo ""
                echo "✅ ProMax 部署成功完成！"
                echo "🌐 访问地址: http://${DEPLOY_SERVER_ip}:${PORT1}"

                # 清理临时文件
                rm -rf ${var_file_path} || true
                '''
            }
        }
    }
}