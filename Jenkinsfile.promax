pipeline {
    agent {
        label 'main'
    }
    
    parameters {
        booleanParam(
            name: 'TESTING',
            defaultValue: false,
            description: '测试跳过环节'
        )
        booleanParam(
            name: 'FORCE_REBUILD_RUNTIME',
            defaultValue: false,
            description: '强制重建基础镜像 promax-runtime'
        )
        string(
            name: 'DEPLOY_BRANCH',
            defaultValue: 'master',
            description: '部署分支'
        )
        string(
            name: 'IMAGE_TAG',
            defaultValue: '',
            description: '镜像标签（留空则使用BUILD_NUMBER）'
        )
        choice(
            name: 'SERVER_ALIAS',
            choices: ['nil','dev005','dev006'],
            description: '服务器别名'
        )
        choice(
            name: 'DEPLOYENV',
            choices: ['promaxtest.yitaiyitai.com'],
            description: '部署环境'
        )
    }
    
    environment {
        // 基础配置 - 将从envs/.env_${params.DEPLOYENV}文件中加载更多变量
        BUILD_TIMESTAMP = sh(script: "date '+%Y%m%d_%H%M%S'", returnStdout: true).trim()
        // 定义缓存文件路径，确保在所有阶段都可用
        SERVERS_CACHE_file = "./servers/server_alias.data"
        DEPLOY_CACHE_file = "/var/jenkins_home/deploy-records.data"
    }
    
    stages {
        stage('加载环境变量') {
            steps {
                script {
                    echo "📋 加载环境变量配置"
                    
                    // 检查环境变量文件是否存在
                    def envFileName = "envs/.env_${params.DEPLOYENV}"
                    if (!fileExists(envFileName)) {
                        error "❌ ${envFileName} 文件不存在，请确保文件存在"
                    }
                    
                    // 加载环境变量文件中的环境变量
                    def envVars = readFile(envFileName)
                        .split('\n')
                        .findAll { line -> 
                            line.trim() && !line.trim().startsWith('#') && line.contains('=')
                        }
                        .collectEntries { line ->
                            def parts = line.split('=', 2)
                            [(parts[0].trim()): parts[1].trim()]
                        }
                    
                    // 设置环境变量
                    envVars.each { key, value ->
                        env."${key}" = value
                    }
                    
                    // 设置构建相关的动态变量
                    env.IMAGE_TAG_FINAL = params.IMAGE_TAG ?: BUILD_NUMBER
                    env.FULL_IMAGE_NAME = "${env.REGISTRY_URL}/${env.IMAGE_NAME}:${env.IMAGE_TAG_FINAL}"
                    
                    echo "✅ 环境变量加载完成"
                    echo "已加载 ${envVars.size()} 个环境变量"
                }
            }
        }
        
        stage('准备阶段') {
            steps {
                script {
                    echo "🚀 开始 ProMax 部署流程"
                    echo "================================"
                    echo "构建编号: ${BUILD_NUMBER}"
                    echo "构建时间: ${BUILD_TIMESTAMP}"
                    echo "提交哈希: ${env.COMMIT_HASH}"
                    echo "部署分支: ${params.DEPLOY_BRANCH}"
                    echo "镜像名称: ${env.FULL_IMAGE_NAME}"
                    echo "容器名称: ${env.CONTAINER_NAME}"
                    echo "镜像仓库: ${env.REGISTRY_URL}"
                    echo "================================"
                    
                    // 清理并重建临时目录，参照 magicreport 的模式
                    sh """
                        var_file_path=./tmpdata/${BUILD_NUMBER}
                        rm -rf "\$var_file_path"
                        mkdir -p "\$var_file_path"
                        chmod 777 -R "\$var_file_path"
                        
                        # 创建 DEPLOY_summary 文件用于记录部署日志
                        touch "\${var_file_path}/DEPLOY_summary"
                        
                        # 保存已知的构建变量到独立文件
                        echo "${BUILD_NUMBER}" > "\${var_file_path}/BUILD_NUMBER"
                        echo "${BUILD_URL}" > "\${var_file_path}/BUILD_URL"
                        echo "${JOB_NAME}" > "\${var_file_path}/JOB_NAME"
                        echo "${params.DEPLOY_BRANCH}" > "\${var_file_path}/BRANCH_NAME"
                        echo "${params.DEPLOYENV}" > "\${var_file_path}/DEPLOYENV"
                        echo "${params.SERVER_ALIAS}" > "\${var_file_path}/SERVER_ALIAS"
                        echo "\$(TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S%Z")" > "\${var_file_path}/BUILD_TIMESTAMP"
                        
                        # 记录准备阶段完成
                        echo "====== 准备阶段完成 ======" >> "\${var_file_path}/DEPLOY_summary"
                    """
                }
            }
        }
        
        stage('代码检出') {
            steps {
                script {
                    echo "📥 检出代码分支: ${params.DEPLOY_BRANCH}"
                    
                    // 检出指定分支
                    checkout([
                        $class: 'GitSCM', 
                        branches: [[name: "refs/heads/${params.DEPLOY_BRANCH}"]], 
                            userRemoteConfigs: [
                            [url: "${env.GIT_REPO_URL}", credentialsId: "${env.GIT_CREDENTIALS_ID}"]
                        ]
                    ])
                    
                    // 配置 git 安全目录并获取提交哈希，参照 magicreport 的模式
                    def JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                    sh "git config --global --add safe.directory ${JOB_PWD}"
                    
                    env.COMMIT_HASH = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                    echo "📝 提交哈希: ${env.COMMIT_HASH}"
                    echo "工作目录: ${JOB_PWD}"
                    
                    // 保存 COMMIT_HASH 到 tmpdata 目录
                    sh """
                        echo "${env.COMMIT_HASH}" > ./tmpdata/${BUILD_NUMBER}/COMMIT_HASH
                        echo "保存提交哈希到 ./tmpdata/${BUILD_NUMBER}/COMMIT_HASH"
                        
                        # 记录代码检出完成
                        echo "====== 代码检出完成，COMMIT_HASH: ${env.COMMIT_HASH} ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                }
            }
        }
        
        stage('构建基础镜像') {
            when {
                expression { params.FORCE_REBUILD_RUNTIME == true }
            }
            steps {
                script {
                    // 构建基础镜像
                    env.BASE_IMAGE_TAG = "${env.BUILD_TIMESTAMP}"
                    env.BASE_IMAGE_NAME = "${env.REGISTRY_URL}/${env.IMAGE_NAME}-base:${env.BASE_IMAGE_TAG}"
                    
                    echo "🏗️ 构建基础镜像: ${env.BASE_IMAGE_NAME}"
                    sh "docker build -f docker/Dockerfile.runtime -t ${env.BASE_IMAGE_NAME} ."
                    
                    echo "📤 推送基础镜像到仓库"
                    sh "docker push ${env.BASE_IMAGE_NAME}"
                    
                    echo "✅ 基础镜像构建和推送完成"
                }
            }
        }
        
        stage('获取最新基础镜像标签') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    // 获取最新的基础镜像标签
                    try {
                        def latestBaseTag = sh(
                            script: "docker images --format 'table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}' | grep '${env.REGISTRY_URL}/${env.IMAGE_NAME}-base' | head -1 | awk '{print \$1}' | cut -d':' -f2",
                            returnStdout: true
                        ).trim()
                        
                        if (latestBaseTag) {
                            env.LATEST_BASE_IMAGE_TAG = latestBaseTag
                        } else {
                            // 如果没有找到本地基础镜像，使用默认标签
                            env.LATEST_BASE_IMAGE_TAG = "latest"
                        }
                    } catch (Exception e) {
                        echo "⚠️ 获取基础镜像标签失败，使用默认标签: ${e.message}"
                        env.LATEST_BASE_IMAGE_TAG = "latest"
                    }
                    
                    env.LATEST_BASE_IMAGE_NAME = "${env.REGISTRY_URL}/${env.IMAGE_NAME}-base:${env.LATEST_BASE_IMAGE_TAG}"
                    echo "📋 使用基础镜像: ${env.LATEST_BASE_IMAGE_NAME}"
                }
            }
        }
        
        stage('构建应用镜像') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    // 动态更新Dockerfile中的FROM镜像
                    sh '''
                        # 备份原始Dockerfile
                        cp docker/Dockerfile.promax.production docker/Dockerfile.promax.production.bak
                        
                        # 更新FROM镜像为最新的基础镜像
                        sed -i.tmp "s|FROM registry.yitaiyitai.com/promax/promax-base:v1|FROM ${env.LATEST_BASE_IMAGE_NAME}|g" docker/Dockerfile.promax.production
                        
                        echo "📝 已更新Dockerfile.promax.production中的基础镜像为: ${env.LATEST_BASE_IMAGE_NAME}"
                    '''
                    
                    // 将构建时需要的环境变量文件复制到构建上下文中
                    def envFileName = "envs/.env_${params.DEPLOYENV}"
                    if (!fileExists(envFileName)) {
                        error "❌ 构建环境变量文件 ${envFileName} 不存在！"
                    }
                    
                    try {
                        sh "cp ${envFileName} ./.env"
                        echo "🐳 构建应用镜像: ${env.FULL_IMAGE_NAME}"
                        sh "docker build -f docker/Dockerfile.promax.production -t ${env.FULL_IMAGE_NAME} ."
                    } finally {
                        // 清理临时的环境变量文件
                        sh "rm ./.env"
                    }
                    
                    echo "📤 推送应用镜像到仓库"
                    sh "docker push ${env.FULL_IMAGE_NAME}"
                    
                    // 恢复原始Dockerfile
                    sh "mv docker/Dockerfile.promax.production.bak docker/Dockerfile.promax.production"
                    
                    echo "✅ 应用镜像构建和推送完成"
                }
            }
        }
        
        stage('生成最终部署配置') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "🔄 动态生成最终的 docker-compose.yml"

                    // 1. 读取 .env 文件
                    def envFileName = "envs/.env_${params.DEPLOYENV}"
                    if (!fileExists(envFileName)) {
                        error "❌ 环境变量文件 ${envFileName} 不存在！"
                    }
                    def envContent = readFile(envFileName)
                    
                    // 2. 格式化环境变量为 YAML map
                    def formattedEnvs = envContent.split('\n').findAll { line ->
                        line.trim() && !line.trim().startsWith('#') && line.contains('=')
                    }.collect { line ->
                        def parts = line.split('=', 2)
                        def key = parts[0].trim()
                        def value = parts[1].trim()
                        // 6 spaces indentation for key
                        "      ${key}: '${value.replace("'", "''")}'"
                    }.join('\n')

                    // 3. 读取 docker-compose 模板
                    def composeTemplate = readFile('docker-compose-promax.yml')

                    // 4. 替换镜像地址占位符
                    def composeWithImage = composeTemplate.replace('${IMAGE}', "${env.FULL_IMAGE_NAME}")

                    // 5. 替换环境变量占位符
                    def finalComposeContent = composeWithImage.replace(
                        '# DYNAMIC_ENVIRONMENT_VARIABLES_PLACEHOLDER',
                        formattedEnvs
                    )

                    // 6. 写入最终的 docker-compose.yml 文件
                    writeFile file: 'docker-compose.yml', text: finalComposeContent

                    echo "✅ 最终 docker-compose.yml 生成完毕"
                    echo "🔍 生成的配置内容预览:"
                    sh 'cat docker-compose.yml'
                }
            }
        }
        
        stage('读取缓存信息') {

            steps {
                script {
                    // 检查缓存文件是否存在
                    def serversCacheExists = fileExists(env.SERVERS_CACHE_file)
                    def deployCacheExists = fileExists(env.DEPLOY_CACHE_file)
                    
                    echo "服务器缓存文件存在: ${serversCacheExists}"
                    echo "部署记录文件存在: ${deployCacheExists}"
                    echo "SERVER_ALIAS: ${params.SERVER_ALIAS}"
                    echo "DEPLOYENV: ${params.DEPLOYENV}"
                    
                    // 初始化部署相关变量
                    env.DEPLOY_SERVER_ip = ""
                    env.DEPLOY_SERVER_port = ""
                    env.DEPLOY_SERVER_domain = ""
                    env.DEPLOY_type = "CREATE"
                    env.DEPLOY_SERVER_ip_origin = ""
                    
                    // 从部署记录中查找已有部署信息
                    if (deployCacheExists) {
                        def deployRecord = sh(
                            script: "grep '${params.DEPLOYENV}' '${env.DEPLOY_CACHE_file}' | tail -1 || echo ''",
                            returnStdout: true
                        ).trim()
                        
                        if (deployRecord) {
                            echo "====== 找到已有部署记录: ${deployRecord} ======"
                            def parts = deployRecord.split(/\s+/)
                            if (parts.size() >= 4) {
                                env.DEPLOY_SERVER_ip_origin = parts[0]
                                env.DEPLOY_SERVER_port = parts[1]
                                env.DEPLOY_SERVER_domain = params.DEPLOYENV

                                // 根据SERVER_ALIAS判断部署类型
                                if (params.SERVER_ALIAS == 'nil') {
                                    env.DEPLOY_type = "UPDATE"
                                    env.DEPLOY_SERVER_ip = env.DEPLOY_SERVER_ip_origin
                                    echo "====== SERVER_ALIAS is nil, deploy update!!! ======"
                                    echo "====== DEPLOY_SERVER_ip: ${env.DEPLOY_SERVER_ip} ======"
                                } else {
                                    echo "====== SERVER_ALIAS exist: ${params.SERVER_ALIAS}, deploy create and delete!!! ======"
                                    // 从服务器别名文件中查找新的服务器IP
                                    if (serversCacheExists) {
                                        def newServerIp = sh(
                                            script: "grep '${params.SERVER_ALIAS}' '${env.SERVERS_CACHE_file}' | awk -F'=' '{print \$2}' || echo ''",
                                            returnStdout: true
                                        ).trim()
                                        
                                        if (newServerIp) {
                                            env.DEPLOY_SERVER_ip = newServerIp
                                            env.DEPLOY_SERVER_domain = params.DEPLOYENV
                                            
                                            // 检查IP是否相同
                                            if (env.DEPLOY_SERVER_ip_origin == env.DEPLOY_SERVER_ip) {
                                                echo "====== IP addresses are the same ======"
                                                env.DEPLOY_type = "UPDATE"
                                            } else {
                                                env.DEPLOY_type = "CREATE_DELETE"
                                            }
                                            echo "====== DEPLOY_SERVER_ip_origin: ${env.DEPLOY_SERVER_ip_origin} ======"
                                            echo "====== DEPLOY_SERVER_ip: ${env.DEPLOY_SERVER_ip} ======"
                                        } else {
                                            echo "未找到SERVER_ALIAS对应的服务器IP"
                                            env.DEPLOY_SERVER_ip = env.DEPLOY_SERVER_ip_origin
                                            env.DEPLOY_type = "UPDATE"
                                            echo "====== DEPLOY_SERVER_ip: ${env.DEPLOY_SERVER_ip} ======"
                                        }
                                    } else {
                                        env.DEPLOY_SERVER_ip = env.DEPLOY_SERVER_ip_origin
                                        env.DEPLOY_type = "UPDATE"
                                        echo "====== DEPLOY_SERVER_ip: ${env.DEPLOY_SERVER_ip} ======"
                                    }
                                }
                            }
                        } else {
                            echo "====== deploy record is nil ======"
                            // 新部署情况
                            if (params.SERVER_ALIAS == 'nil') {
                                echo "====== SERVER_ALIAS is nil, deploy fail!!! ======"
                                error "SERVER_ALIAS为nil且无已有部署记录，无法确定部署服务器"
                            } else {
                                echo "====== SERVER_ALIAS: ${params.SERVER_ALIAS} ======"
                                // 从服务器别名文件中查找服务器IP
                                if (serversCacheExists) {
                                    def serverIp = sh(
                                        script: "grep '${params.SERVER_ALIAS}' '${env.SERVERS_CACHE_file}' | awk -F'=' '{print \$2}' || echo ''",
                                        returnStdout: true
                                    ).trim()
                                    
                                    if (serverIp) {
                                        env.DEPLOY_SERVER_ip = serverIp
                                        env.DEPLOY_SERVER_domain = params.DEPLOYENV
                                        env.DEPLOY_type = "CREATE"
                                        echo "====== DEPLOY_SERVER_ip: ${env.DEPLOY_SERVER_ip} ======"
                                    } else {
                                        error "未找到SERVER_ALIAS '${params.SERVER_ALIAS}' 对应的服务器IP"
                                    }
                                } else {
                                    error "服务器缓存文件不存在，无法查找SERVER_ALIAS"
                                }
                            }
                        }
                    } else {
                        // 部署记录文件不存在的情况
                        if (params.SERVER_ALIAS == 'nil') {
                            echo "====== SERVER_ALIAS is nil, deploy fail!!! ======"
                            error "SERVER_ALIAS为nil且无部署记录文件，无法确定部署服务器"
                        } else {
                            echo "====== SERVER_ALIAS: ${params.SERVER_ALIAS} ======"
                            // 从服务器别名文件中查找服务器IP
                            if (serversCacheExists) {
                                def serverIp = sh(
                                    script: "grep '${params.SERVER_ALIAS}' '${env.SERVERS_CACHE_file}' | awk -F'=' '{print \$2}' || echo ''",
                                    returnStdout: true
                                ).trim()
                                
                                if (serverIp) {
                                    env.DEPLOY_SERVER_ip = serverIp
                                    env.DEPLOY_SERVER_domain = params.DEPLOYENV
                                    env.DEPLOY_type = "CREATE"
                                    echo "====== DEPLOY_SERVER_ip: ${env.DEPLOY_SERVER_ip} ======"
                                } else {
                                    error "未找到SERVER_ALIAS '${params.SERVER_ALIAS}' 对应的服务器IP"
                                }
                            } else {
                                error "服务器缓存文件不存在，无法查找SERVER_ALIAS"
                            }
                        }
                    }
                    
                    echo "====== check important vars: ip ${env.DEPLOY_SERVER_ip}, port ${env.DEPLOY_SERVER_port}, domain ${env.DEPLOY_SERVER_domain}, ip_origin ${env.DEPLOY_SERVER_ip_origin}, type ${env.DEPLOY_type} ======"
                }
            }
        }
        
        stage('分配端口') {

            steps {
                script {
                    echo "🔍 分配动态端口..."
                    
                    // 定义probe_port函数 (与jenkins-tpl保持一致)
                    def probePort = { serverHost, count ->
                        def portScript = """
#!/bin/bash
available_ports=""
port_count_found=0

# 在35000-36000范围内查找可用端口
for i in \$(seq 35000 36000); do
    echo "=== probe ${serverHost} \$i ==="
    result=\$(timeout 1 telnet ${serverHost} \$i 2>&1)
    r=\$(echo \$result | grep -v "Escape character")
    if [ -n "\$r" ]; then
        # 端口未被占用，添加到可用端口列表
        if [ -z "\$available_ports" ]; then
            available_ports="\$i"
        else
            available_ports="\$available_ports,\$i"
        fi
        port_count_found=\$((port_count_found + 1))
        echo "Port \$i is available"
        
        # 如果已找到足够数量的可用端口，则退出循环
        if [ \$port_count_found -eq ${count} ]; then
            break
        fi
    fi
done

# 如果找到了足够数量的端口，则输出端口列表
if [ \$port_count_found -eq ${count} ]; then
    echo "Found ${count} available ports: \$available_ports"
    echo \$available_ports
else
    echo "Failed to find ${count} available ports, only found \$port_count_found: \$available_ports" >&2
    # 如果至少找到了一个端口，也返回
    if [ \$port_count_found -gt 0 ]; then
        echo \$available_ports
    else
        echo ""
    fi
fi
                        """
                        
                        def result = sh(
                            script: portScript,
                            returnStdout: true
                        ).trim()
                        
                        // 提取最后一行作为端口列表
                        def lines = result.split('\n')
                        return lines[-1]
                    }
                    
                    // 根据部署类型决定端口分配策略
                    if (env.DEPLOY_type == "UPDATE" && env.DEPLOY_SERVER_port && env.DEPLOY_SERVER_port != "FILE_FREEZE") {
                        // UPDATE类型：重用已有端口
                        def cachedPorts = env.DEPLOY_SERVER_port.split(",")
                        if (cachedPorts.size() >= 3) {
                            env.PORT1 = cachedPorts[0]
                            env.PORT2 = cachedPorts[1] 
                            env.PORT3 = cachedPorts[2]
                            echo "重用缓存端口: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3}"
                        } else if (cachedPorts.size() == 1) {
                            // 只有一个端口的情况，分配另外两个
                            env.PORT1 = cachedPorts[0]
                            def deployServerIp = env.DEPLOY_SERVER_ip
                            def availablePorts = probePort(deployServerIp, 2)
                            def portList = availablePorts.split(',')
                            
                            if (portList.size() >= 2) {
                                env.PORT2 = portList[0]
                                env.PORT3 = portList[1]
                                echo "扩展端口分配: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3}"
                            } else {
                                error "❌ 无法找到足够的额外端口"
                            }
                        } else {
                            // 缓存端口不完整，重新分配
                            def deployServerIp = env.DEPLOY_SERVER_ip
                            def availablePorts = probePort(deployServerIp, 3)
                            def portList = availablePorts.split(',')
                            
                            if (portList.size() < 3) {
                                error "❌ 无法找到足够的可用端口，只找到 ${portList.size()} 个端口"
                            }
                            
                            env.PORT1 = portList[0]
                            env.PORT2 = portList[1]
                            env.PORT3 = portList[2]
                            echo "缓存端口不完整，重新分配: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3}"
                        }
                    } else if (env.DEPLOY_type == "CREATE" || env.DEPLOY_type == "CREATE_DELETE") {
                        // CREATE或CREATE_DELETE类型：探测新端口
                        echo "====== create port ======"
                        def deployServerIp = env.DEPLOY_SERVER_ip
                        
                        // 检查是否为内网IP (192.168.31.x)
                        if (deployServerIp.startsWith("192.168.31.")) {
                            // 内网服务器，探测端口
                            def availablePorts = probePort(deployServerIp, 3)
                            def portList = availablePorts.split(',')
                            
                            if (portList.size() < 3) {
                                error "❌ 无法找到足够的可用端口，只找到 ${portList.size()} 个端口"
                            }
                            
                            env.PORT1 = portList[0]  // 主应用端口
                            env.PORT2 = portList[1]  // API端口
                            env.PORT3 = portList[2]  // Python挖掘服务端口
                            echo "新分配端口: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3}"
                        } else {
                            // 外网服务器，使用FILE_FREEZE标记
                            env.DEPLOY_SERVER_port = "FILE_FREEZE"
                            env.PORT1 = "35000"  // 默认端口
                            env.PORT2 = "35001"
                            env.PORT3 = "35002"
                            echo "外网服务器，使用默认端口: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3}"
                        }
                    } else {
                        // 其他情况，使用默认端口
                        env.PORT1 = "35000"
                        env.PORT2 = "35001"
                        env.PORT3 = "35002"
                        echo "使用默认端口: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3}"
                    }
                    
                    // 更新DEPLOY_SERVER_port为逗号分隔的端口列表
                    env.DEPLOY_SERVER_port = "${env.PORT1},${env.PORT2},${env.PORT3}"
                    
                    echo "✅ 端口分配完成:"
                    echo "   主应用端口: ${env.PORT1}"
                    echo "   API端口: ${env.PORT2}"
                    echo "   Python挖掘服务端口: ${env.PORT3}"
                    
                    // 保存部署相关信息到独立文件，参照 magicreport 模式
                    sh """
                        # 保存端口信息
                        echo "${env.PORT1}" > ./tmpdata/${BUILD_NUMBER}/PORT1
                        echo "${env.PORT2}" > ./tmpdata/${BUILD_NUMBER}/PORT2
                        echo "${env.PORT3}" > ./tmpdata/${BUILD_NUMBER}/PORT3
                        
                        # 保存部署服务器信息
                        echo "${env.DEPLOY_SERVER_port}" > ./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_port
                        echo "${env.DEPLOY_SERVER_ip}" > ./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip
                        echo "${env.DEPLOY_SERVER_domain}" > ./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_domain
                        echo "${env.DEPLOY_type}" > ./tmpdata/${BUILD_NUMBER}/DEPLOY_type
                        echo "${env.DEPLOY_SERVER_ip_origin}" > ./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip_origin
                        
                        # 记录端口分配完成
                        echo "====== 端口分配完成: PORT1=${env.PORT1}, PORT2=${env.PORT2}, PORT3=${env.PORT3} ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                }
            }
        }
        
        stage('准备部署文件') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "📤 准备部署文件..."
                    
                    // 使用模板文件生成最终的docker-compose.yml
                    sh """
                        echo "生成docker-compose.yml文件..."
                        cp docker-compose-promax.yml docker-compose-generated.yml
                        
                        # 替换变量
                        sed -i 's/\${IMAGE}/${env.FULL_IMAGE_NAME}/g' docker-compose-generated.yml
                        sed -i 's/\${PORT1}/${env.PORT1}/g' docker-compose-generated.yml
                        sed -i 's/\${PORT2}/${env.PORT2}/g' docker-compose-generated.yml
                        sed -i 's/\${PORT3}/${env.PORT3}/g' docker-compose-generated.yml
                        
                        # 替换其他环境变量
                        sed -i "s|\${REMOTE_DEPLOY_DIR}|${env.REMOTE_DEPLOY_DIR}|g" docker-compose-generated.yml
                        sed -i "s|\${GIT_REPO_URL}|${env.GIT_REPO_URL}|g" docker-compose-generated.yml
                        sed -i 's/\${GIT_CREDENTIALS_ID}/${env.GIT_CREDENTIALS_ID}/g' docker-compose-generated.yml
                        sed -i "s|\${REGISTRY_URL}|${env.REGISTRY_URL}|g" docker-compose-generated.yml
                    """
                    
                    // 清除known_hosts记录，确保SSH连接安全
                    sh """
                        ssh-keygen -f "${HOME}/.ssh/known_hosts" -R "${env.DEPLOY_SERVER_ip}" || true
                        echo "====== 清除 known_hosts 记录完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 创建远程部署目录
                    sh """
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "mkdir -p ${env.REMOTE_DEPLOY_DIR}"
                        echo "====== 创建远程部署目录完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 同步生成的docker-compose.yml到远程服务器
                    sh """
                        echo "同步docker-compose.yml到远程服务器..."
                        scp -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" docker-compose-generated.yml root@${env.DEPLOY_SERVER_ip}:${env.REMOTE_DEPLOY_DIR}/docker-compose.yml
                        echo "====== 同步 docker-compose.yml 完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 同步其他部署相关文件
                    sh """
                        rsync -avz -e "ssh -o StrictHostKeyChecking=no -i ${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" \
                            config/ \
                            scripts/load-env.sh \
                            root@${env.DEPLOY_SERVER_ip}:${env.REMOTE_DEPLOY_DIR}/
                        echo "====== 同步部署相关文件完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 恢复原始docker-compose.yml
                    sh "mv docker-compose.yml.bak docker-compose.yml"
                    
                    echo "✅ 部署文件准备完成"
                }
            }
        }
        

        
        stage('部署服务') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "🚀 开始部署服务..."
                    
                    // 拉取最新镜像
                    sh """
                        echo "📥 拉取最新镜像..."
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "docker pull ${env.FULL_IMAGE_NAME}"
                        echo "====== 拉取最新镜像完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    sh """
                        echo "🛑 停止现有服务..."
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "cd ${env.REMOTE_DEPLOY_DIR} && docker-compose down || true"
                        echo "====== 停止现有服务完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 启动服务
                    sh """
                        echo "🚀 启动服务..."
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "cd ${env.REMOTE_DEPLOY_DIR} && docker-compose up -d"
                        echo "====== 启动服务完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                }
            }
        }
        
        stage('健康检查') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "🔍 执行健康检查..."
                    
                    // 等待服务启动
                    sleep(time: 30, unit: 'SECONDS')
                    
                    // 检查docker-compose服务状态
                    sh """
                        echo "检查服务状态..."
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "cd ${env.REMOTE_DEPLOY_DIR} && docker-compose ps"
                        echo "====== 服务状态检查完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 检查容器日志
                    sh """
                        echo "检查容器日志..."
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "cd ${env.REMOTE_DEPLOY_DIR} && docker-compose logs --tail=20 ${env.CONTAINER_NAME} || echo '⚠️ 日志检查失败'"
                        echo "====== 容器日志检查完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    // 测试API端点
                    sh """
                        echo "测试API端点..."
                        ssh -o StrictHostKeyChecking=no -i "${HOME}/.ssh/${env.DEPLOY_SERVER_ip}.pem" root@${env.DEPLOY_SERVER_ip} \
                        "curl -f http://localhost:${env.PORT1}/api/health 2>/dev/null || echo '⚠️ API健康检查失败'"
                        echo "====== API端点测试完成 ======" >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                    """
                    
                    echo "✅ 健康检查完成"
                }
            }
        }
        
        stage('更新部署记录') {
            when {
                expression { params.TESTING == true }
            }
            steps {
                script {
                    echo "📝 更新部署记录..."
                    
                    // 确保全局部署记录文件存在且有正确权限
                    sh """
                        # 确保 /var/jenkins_home 目录存在
                        sudo mkdir -p /var/jenkins_home
                        
                        # 如果部署记录文件不存在，创建它并设置权限
                        if [ ! -f '${env.DEPLOY_CACHE_file}' ]; then
                            sudo touch '${env.DEPLOY_CACHE_file}'
                            sudo chmod 666 '${env.DEPLOY_CACHE_file}'
                            echo "创建全局部署记录文件: ${env.DEPLOY_CACHE_file}"
                        fi
                        
                        # 确保当前用户有读写权限
                        sudo chmod 666 '${env.DEPLOY_CACHE_file}'
                    """
                    
                    // 清理历史记录（使用更安全的方式）
                    if (fileExists(env.DEPLOY_CACHE_file)) {
                        sh """
                            # 创建临时文件进行安全的记录清理
                            temp_file="\$(mktemp)"
                            grep -v '${params.DEPLOYENV}' '${env.DEPLOY_CACHE_file}' > "\$temp_file" || true
                            mv "\$temp_file" '${env.DEPLOY_CACHE_file}'
                            chmod 666 '${env.DEPLOY_CACHE_file}'
                        """
                    }
                    
                    // 写入新的部署记录（原子性操作）
                    // 格式: IP PORT DOMAIN TIMESTAMP HASH
                    def timestamp = sh(
                        script: "TZ='Asia/Shanghai' date '+%Y-%m-%dT%H:%M:%S%Z'",
                        returnStdout: true
                    ).trim()
                    
                    def deployRecord = "${env.DEPLOY_SERVER_ip} ${env.DEPLOY_SERVER_port} ${params.DEPLOYENV} ${timestamp} promax::${env.COMMIT_HASH}"
                    
                    // 使用原子性写入避免并发问题
                    sh """
                        # 使用文件锁确保原子性写入
                        (
                            flock -x 200
                            echo '${deployRecord}' >> '${env.DEPLOY_CACHE_file}'
                            chmod 666 '${env.DEPLOY_CACHE_file}'
                        ) 200>'${env.DEPLOY_CACHE_file}.lock'
                        
                        # 清理锁文件
                        rm -f '${env.DEPLOY_CACHE_file}.lock'
                    """
                    echo "已更新部署记录: ${deployRecord}"
                    
                    echo "✅ 部署记录更新完成"
                }
            }
        }
    }
    
    post {
        always {
            script {
                
                // 从临时文件中读取部署服务器IP
                def deployServerIp = ""
                try {
                    deployServerIp = readFile("./tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip").trim()
                } catch (Exception e) {
                    deployServerIp = env.DEPLOY_SERVER_ip ?: ""
                }
                
                // 生成部署报告
                def deployReport = """
🎉 ProMax 部署完成报告
================================
构建编号: ${BUILD_NUMBER}
构建时间: ${BUILD_TIMESTAMP}
提交哈希: ${env.COMMIT_HASH}
部署分支: ${params.DEPLOY_BRANCH}
镜像名称: ${env.FULL_IMAGE_NAME}
目标服务器: root@${deployServerIp}
容器名称: ${env.CONTAINER_NAME}
部署类型: ${env.DEPLOY_type}

端口分配:
- 主应用端口: ${env.PORT1} (映射到容器内3100)
- API端口: ${env.PORT2} (映射到容器内3101)
- Python挖掘服务端口: ${env.PORT3} (映射到容器内8000)

访问地址:
- 主应用: http://${env.DEPLOY_SERVER_ip}:${env.PORT1}
- API接口: http://${env.DEPLOY_SERVER_ip}:${env.PORT2}/api
- API文档: http://${env.DEPLOY_SERVER_ip}:${env.PORT2}/api/docs
- 挖掘服务: http://${env.DEPLOY_SERVER_ip}:${env.PORT3}
- 挖掘服务文档: http://${env.DEPLOY_SERVER_ip}:${env.PORT3}/docs

管理命令:
- 查看服务状态: ssh root@${deployServerIp} 'cd ${env.REMOTE_DEPLOY_DIR} && docker-compose ps'
- 查看服务日志: ssh root@${deployServerIp} 'cd ${env.REMOTE_DEPLOY_DIR} && docker-compose logs -f'
- 重启服务: ssh root@${deployServerIp} 'cd ${env.REMOTE_DEPLOY_DIR} && docker-compose restart'
- 停止服务: ssh root@${deployServerIp} 'cd ${env.REMOTE_DEPLOY_DIR} && docker-compose down'
- 启动服务: ssh root@${deployServerIp} 'cd ${env.REMOTE_DEPLOY_DIR} && docker-compose up -d'
- 进入容器: ssh root@${deployServerIp} 'cd ${env.REMOTE_DEPLOY_DIR} && docker-compose exec ${env.CONTAINER_NAME} bash'

部署目录: ${env.REMOTE_DEPLOY_DIR}
================================
"""
                
                writeFile file: "./tmpdata/${BUILD_NUMBER}/deploy-report.txt", text: deployReport
                echo deployReport
                
                // 显示部署摘要，参照 magicreport 模式
                sh "cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary || echo '无部署摘要信息'"
                
                // 清理临时文件
                sh "rm -rf ./tmpdata/${BUILD_NUMBER} || true"
            }
        }
        
        success {
            script {
                echo "✅ ProMax 部署成功完成！"
                
                // 显示部署摘要
                if (fileExists("./tmpdata/${BUILD_NUMBER}/DEPLOY_summary")) {
                    echo "📋 部署摘要:"
                    sh "cat ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary"
                }
                
                // 显示访问地址
                if (env.DEPLOY_SERVER_ip && env.PORT1) {
                    echo "🌐 访问地址: http://${env.DEPLOY_SERVER_ip}:${env.PORT1}"
                }
            }
        }
        
        failure {
            script {
                echo "❌ ProMax 部署失败！"
            }
        }
    }
}