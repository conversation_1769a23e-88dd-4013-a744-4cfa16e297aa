services:
  promax-platform:
    image: ${IMAGE}
    container_name: promax-platform
    restart: unless-stopped
    
    # GPU 支持 (Docker Compose 2.3+ 支持)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    ports:
      - "${PORT1}:3100"  # 主应用端口
      - "${PORT2}:3101"  # API端口
      - "${PORT3}:8000"  # Python挖掘服务端口
    
    volumes:
      # 数据持久化
      - promax_logs:/app/logs
      - promax_uploads:/app/uploads
      - promax_reports:/app/reports
      - promax_result_data:/app/result-data
      - promax_supervisor_logs:/var/log/supervisor
      # 配置文件
      - ./config:/app/config:ro
    
    environment:
      # DYNAMIC_ENVIRONMENT_VARIABLES_PLACEHOLDER
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - promax_network

networks:
  promax_network:
    driver: bridge

volumes:
  promax_logs:
    driver: local
  promax_uploads:
    driver: local
  promax_reports:
    driver: local
  promax_result_data:
    driver: local
  promax_supervisor_logs:
    driver: local