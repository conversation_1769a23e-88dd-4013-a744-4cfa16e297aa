pipeline {
    agent none

    environment {
        COMMIT_HASH_bpmax_helper = ''
        COMMIT_HASH_hpt_frontend = ''
        COMMIT_HASH_hpt_mobile = ''
        COMMIT_HASH_huanpingtong_server = ''
        COMMIT_HASH_bpmax_server = ''
    }

    stages {
        stage('Set Name') {
            agent {
                label 'main'
            }
            steps {
                script {
                    currentBuild.description = "DeployEnv: ${DEPLOYENV} \n" + 
                        "bpmax-helper: ${branch_bpmax_helper} \n" + 
                        "hpt-frontend: ${branch_hpt_frontend} \n" + 
                        "hpt-mobile: ${branch_hpt_mobile} \n" + 
                        "huanpingtong-server: ${branch_huanpingtong_server} \n" + 
                        "bpmax-server: ${branch_bpmax_server} \n"
                }
            }
        }

        stage('Build Project') {
            parallel {
                
                stage('Check bpmax-helper') {
                    agent {
                        docker {
                            label 'main'
                            image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
                            args '-v /yarn_cache_aio_1:/usr/local/share/.cache/yarn -u 0:0'
                            reuseNode true
                        }
                    }
                    environment {
                        API_HOST = '/api'
                        JOB_PWD = ''
                    }
                    steps {
                        dir('bpmax-helper') {
                            script {
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                sh '''
                                    git fetch --prune origin
                                    git tag -l | xargs git tag -d
                                '''
                                if (env.BRANCH_TYPE_bpmax_helper.toBoolean()) {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/heads/${branch_bpmax_helper}"]], 
                                        userRemoteConfigs: [
                                        [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                } else {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/tags/${branch_bpmax_helper}"]],
                                        userRemoteConfigs: [
                                        [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                }

                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                COMMIT_HASH_bpmax_helper = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                                echo "bpmax_helper hash"
                                echo COMMIT_HASH_bpmax_helper
                            }
                        }
                    }
                }

                stage('Build hpt-frontend') {
                    agent {
                        docker { 
                            label 'runner'
                            image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
                            args '-v /yarn_cache_aio_1:/usr/local/share/.cache/yarn -u 0:0'
                            reuseNode true
                        }
                    }
                    environment {
                        API_HOST = '/api'
                        JOB_PWD = ''
                    }   
                    steps {
                        sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'
                        sh '''
                            if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                                ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                                rm -rf /usr/local/share/.cache/yarn/v6/.tmp
                            else
                                echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
                            fi
                        '''

                        dir('hpt-frontend') {
                            script {
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                sh '''
                                    git fetch --prune origin
                                    git tag -l | xargs git tag -d
                                '''
                                if (env.BRANCH_TYPE_hpt_frontend.toBoolean()) {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/heads/${branch_hpt_frontend}"]], 
                                        userRemoteConfigs: [
                                        [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                    } else {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/tags/${branch_hpt_frontend}"]],
                                        userRemoteConfigs: [
                                        [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                }

                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                COMMIT_HASH_hpt_frontend = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                                echo "hpt_frontend hash"
                                echo COMMIT_HASH_hpt_frontend
                            }
                            sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
                            sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false && yarn --registry="https://npm.yitaiyitai.com/" --no-cache'
                            sh 'rm -rf node_modules/@bpmax/helper && git clone -b ${branch_bpmax_helper} http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper'
                            sh '''
                                export $(xargs < ../envs/.env_${DEPLOYENV})
                                if [ -n "$PUBLICPATH_PC" ]; then
                                export PUBLIC_PATH=https://$OSSBUCKET.$OSSREEGION.aliyuncs.com$PUBLICPATH_PC
                                fi
                                rm -rf .nuxt dist ../dist_pre && yarn build
                                mkdir -p ../dist_pre/hpt-frontend && rm -rf ../dist_pre/hpt-frontend/*
                                cp -r .nuxt dist static utils nuxt.config.js package.json node_modules ../dist_pre/hpt-frontend/
                                chown -R 1000:1000 ../dist_pre
                                echo $PUBLICPATH_PC
                                if [ -n "$PUBLICPATH_PC" ]; then
                                curl https://gosspublic.alicdn.com/ossutil/install.sh | bash
                                ossutil cp -r -f dist oss://$OSSBUCKET$PUBLICPATH_PC --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --include "*.js" --meta Content-Type:application/javascript
                                ossutil cp -r -f dist oss://$OSSBUCKET$PUBLICPATH_PC --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --exclude "*.js"
                                fi
                                rm -rf node_modules/.cache
                            '''
                        }

                        script {
                            def stashName = "${env.JOB_NAME}_hpt-frontend"
                            stash name: stashName, includes: 'dist_pre/hpt-frontend/**/*'
                        }                        
                    }
                }

                stage('Build hpt-mobile') {
                    agent {
                        docker { 
                        label 'runner'
                        image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
                        reuseNode true
                        args '-v /yarn_cache_aio_2:/usr/local/share/.cache/yarn -u 0:0'
                        }
                    }
                    environment {
                        API_HOST = '/api'
                        JOB_PWD = ''
                    }                    
                    steps {
                        sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'
                        sh '''
                        if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                            ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                            rm -rf /usr/local/share/.cache/yarn/v6/.tmp
                        else
                            echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
                        fi
                        '''

                        dir('hpt-mobile') {
                            script {
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                sh '''
                                    git fetch --prune origin
                                    git tag -l | xargs git tag -d
                                '''
                                if (env.BRANCH_TYPE_hpt_mobile.toBoolean()) {
                                    checkout(
                                        [$class: 'GitSCM', branches: [[name: "refs/heads/${branch_hpt_mobile}"]], 
                                        userRemoteConfigs: [
                                        [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-mobile.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])                    

                                } else {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/tags/${branch_hpt_mobile}"]],
                                        userRemoteConfigs: [
                                        [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-mobile.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                }
                                sh '''
                                echo "===> 缓存目录"
                                pwd
                                '''

                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                COMMIT_HASH_hpt_mobile = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                            }

                            sh '''
                                git config --global user.email "<EMAIL>"
                                git config --global user.name "jenkins"
                            '''
                            sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
                            sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false && yarn --registry="https://npm.yitaiyitai.com/" --no-cache'
                            sh "yarn remove huanpingtong && yarn add huanpingtong@git+http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git#${branch_hpt_frontend} --skip-integrity-check --no-lockfile --force --no-save"
                            sh '''
                                export $(xargs < ../envs/.env_${DEPLOYENV})
                                if [ -n "$PUBLICPATH_MOBILE" ]; then
                                    export PUBLIC_PATH=https://$OSSBUCKET.$OSSREEGION.aliyuncs.com$PUBLICPATH_MOBILE
                                fi
                                rm -rf .nuxt dist ../dist_pre && yarn generate
                                mkdir -p ../dist_pre/hpt-mobile && rm -rf ../dist_pre/hpt-mobile/*
                                cp -r dist ../dist_pre/hpt-mobile/
                                chown -R 1000:1000 ../dist_pre
                                echo $PUBLICPATH_MOBILE
                                if [ -n "$PUBLICPATH_MOBILE" ]; then
                                    curl https://gosspublic.alicdn.com/ossutil/install.sh | bash
                                    ossutil cp -r -f dist/build/m oss://$OSSBUCKET$PUBLICPATH_MOBILE --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --include "*.js" --meta Content-Type:application/javascript
                                    ossutil cp -r -f dist/build/m oss://$OSSBUCKET$PUBLICPATH_MOBILE --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --exclude "*.js"
                                fi
                                rm -rf node_modules/.cache
                            '''
                        }
                        script {
                            def stashName = "${env.JOB_NAME}_hpt-mobile"
                            stash name: stashName, includes: 'dist_pre/hpt-mobile/**/*'
                        }
                    }
                }

                stage('Build huanpingtong-server') {
                    agent {
                        docker { 
                            label 'runner || main'
                            image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
                            reuseNode true
                            args '-v /yarn_cache_aio_3:/usr/local/share/.cache/yarn -u 0:0'
                        }
                    }
                    environment {
                        API_HOST = '/api'
                        JOB_PWD = ''
                    }
                    steps {
                        sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'

                        sh '''
                        if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                            ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                            rm -rf /usr/local/share/.cache/yarn/v6/.tmp
                        else
                            echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
                        fi
                        '''
                        
                        dir('huanpingtong-server') {
                            script {
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                sh '''
                                    git fetch --prune origin
                                    git tag -l | xargs git tag -d
                                '''
                                if (env.BRANCH_TYPE_huanpingtong_server.toBoolean()) {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/heads/${branch_huanpingtong_server}"]], 
                                        userRemoteConfigs: [
                                            [url: 'http://gitlabbot:<EMAIL>/bpmax/huanpingtong-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                } else {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/tags/${branch_huanpingtong_server}"]],
                                        userRemoteConfigs: [
                                            [url: 'http://gitlabbot:<EMAIL>/bpmax/huanpingtong-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                }
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                COMMIT_HASH_huanpingtong_server = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                            }
                            sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
                            sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false'
                            sh 'rm -rf node_modules && yarn add ssh2@1.4.0 --ignore-optional && yarn --registry="https://npm.yitaiyitai.com/" --frozen-lockfile --no-cache'
                            sh "yarn remove huanpingtong && yarn add huanpingtong@git+http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git#${branch_hpt_frontend} --skip-integrity-check --no-lockfile --force --no-save"
                            sh 'rm -rf node_modules/@bpmax/helper && git clone -b ${branch_bpmax_helper} http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper'
                            sh 'rm -rf app ../dist_pre && yarn compile'
                            sh 'mkdir -p ../dist_pre/huanpingtong-server && rm -rf ../dist_pre/huanpingtong-server/*'
                            sh '''
                                sed -i 's/process.exit(a)//' node_modules/lz4-asm/dist/lz4wasm.js
                                sed -i 's/if(!(a instanceof z))throw a;//' node_modules/lz4-asm/dist/lz4wasm.js
                            '''
                            sh 'cp -r app config custom migrations view www nuxt.config.admin.js .db-migraterc package.json production.js node_modules ../dist_pre/huanpingtong-server/; chown -R 1000:1000 ../dist_pre'
                            sh 'rm -rf node_modules/.cache'
                        }

                        script {
                            def stashName = "${env.JOB_NAME}_huanpingtong-server"
                            stash name: stashName, includes: 'dist_pre/huanpingtong-server/**/*'
                        }
                    }
                }

                stage('Build bpmax-server') {
                    agent {
                        docker { 
                            label 'runner'
                            image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
                            reuseNode true
                            args '-v /yarn_cache_aio_4:/usr/local/share/.cache/yarn -u 0:0'
                        }
                    }
                    environment {
                        API_HOST = '/api'
                        JOB_PWD = ''
                    }
                    steps {
                        sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'
                        
                        sh '''
                        if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                            ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                            rm -rf /usr/local/share/.cache/yarn/v6/.tmp
                        else
                            echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
                        fi
                        '''

                        dir('bpmax-server') {
                            script {
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                sh '''
                                    git fetch --prune origin
                                    git tag -l | xargs git tag -d
                                '''
                                if (env.BRANCH_TYPE_bpmax_server.toBoolean()) {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/heads/${branch_bpmax_server}"]], 
                                        userRemoteConfigs: [
                                            [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                } else {
                                    checkout([
                                        $class: 'GitSCM', 
                                        branches: [[name: "refs/tags/${branch_bpmax_server}"]],
                                        userRemoteConfigs: [
                                            [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                                        ]
                                    ])
                                }
                                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                                sh "git config --global --add safe.directory ${JOB_PWD}"
                                COMMIT_HASH_bpmax_server = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                            }
                            sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
                            sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false && yarn --registry="https://npm.yitaiyitai.com/" --no-cache'
                            sh 'echo ==-${branch_bpmax_helper} && rm -rf node_modules/@bpmax/helper && git clone -b ${branch_bpmax_helper} http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper'
                            sh 'rm -rf dist ../dist_pre && yarn build'
                            sh 'mkdir -p ../dist_pre/bpmax-server && rm -rf ../dist_pre/bpmax-server/*'
                            sh 'cp -r dist package.json node_modules ../dist_pre/bpmax-server/; chown -R 1000:1000 ../dist_pre'
                            sh 'rm -rf node_modules/.cache'
                        }

                        script {
                            def stashName = "${env.JOB_NAME}_bpmax-server"
                            stash name: stashName, includes: 'dist_pre/bpmax-server/**/*'
                        }
                        
                    }
                }

            }
        }

        stage('Build Prod Package') {
            agent {
                // 使用标签为ssh的agent
                label 'runner'
            }
            steps {
                dir('docker') {
                    sh 'id'
                    sh 'pwd && rm -rf hpt-frontend hpt-mobile huanpingtong-server bpmax-server bpmax_prod.tar'
                    sh "ls -l"
                }
                sh 'echo $JOB_NAME=============JOB_NAMEJOB_NAMEJOB_NAME============'
                unstash "${env.JOB_NAME}_hpt-frontend"
                unstash "${env.JOB_NAME}_hpt-mobile"
                unstash "${env.JOB_NAME}_huanpingtong-server"
                unstash "${env.JOB_NAME}_bpmax-server"       

                sh "find dist_pre -type f -regex '.*\\.map' -print0 | xargs -0 rm -f && mv dist_pre/* ./docker"

                dir('docker') {
                    sh '''
                        #!/bin/bash
                        TAG=`date +%Y%m%d%H%M`

                        echo "" > ./SERVER_TYPE_ARR_FILE.txt
                        echo "__DEFAULT__" > ./SERVER_TYPE_ARR_FILE.txt

                        # 检查env文件
                        MULTI_SERVER_TYPE=`cat ../envs/.env_${DEPLOYENV} | grep "__AUTO__" | wc -l`

                        if [ "$MULTI_SERVER_TYPE" -eq 1 ];then
                            echo "__AUTO__" >> ./SERVER_TYPE_ARR_FILE.txt
                        fi

                        cat ./SERVER_TYPE_ARR_FILE.txt

                        while read -r line; do

                            rm -f ../envs/.env_current

                            if [ ! -f "../envs/.env_${DEPLOYENV}" ];then
                                continue;
                            fi

                            cp ../envs/.env_${DEPLOYENV} ../envs/.env_current
                            
                            if [ "$line" != "__DEFAULT__" ];then
                                sed -i "s/$line//g" ../envs/.env_current
                            fi

                            sed -n '1,/^WORKDIR \\/var\\/www/p' Dockerfile-k8s > Dockerfile-k8s_tmp
                            mv Dockerfile-k8s_tmp Dockerfile-k8s

                            while IFS='=' read -r key value; do
                                key=$(echo "$key" | xargs)
                                value=$(echo "$value" | xargs)
                                if [ -n "$key" ]; then
                                    if [ -z "$value" ]; then
                                        echo "ENV $key"
                                    else
                                        echo "ENV $key $value" >> Dockerfile-k8s
                                    fi
                                fi
                            done < ../envs/.env_current

                            tryfile=`curl -I -s -o /dev/null -w "%{http_code}\n" https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_runner/crun-$DEPLOYENV | grep '200' | wc -l`
                            if [ "$tryfile" -eq 1 ]; then
                                echo "tryfile case $tryfile"
                                sed -i "s/crun-all/crun-$DEPLOYENV/g" Dockerfile-k8s
                            fi

                            echo "Dockerfile-k8s======>"
                            cat ./Dockerfile-k8s
                            echo "<==="

                            #set +ex
                            #docker rmi registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}:$TAG
                            #set -ex
                            imagetype=`echo $line | awk -F'__' '{print $2}'`
                            lowercase=$(echo "$imagetype" | tr '[:upper:]' '[:lower:]')
                            docker build --no-cache -t registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}-${lowercase}:$TAG -f Dockerfile-k8s .

                            NEW_DEPLOYENV=$(echo "$DEPLOYENV" | sed 's/\\./_/g')
                            
                            # cbd 相关镜像推送到cbd镜像仓库
                            if [ "${DEPLOYENV}" = "xunjian.shuxinyc.com" -o "${DEPLOYENV}" = "yygl.shuxinyc.com" ]; then
                                echo "cbd registry"
                                # b-tradd-prod-registry.cn-hangzhou.cr.aliyuncs.com/bpmax-prod-cd
                                docker login -u "bpmax-acr@1605653470292707" -p "4pN@Y79v" b-tradd-prod-registry.cn-hangzhou.cr.aliyuncs.com
                                docker tag registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}-${lowercase}:$TAG b-tradd-prod-registry.cn-hangzhou.cr.aliyuncs.com/bpmax-prod-cd/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG
                                docker push b-tradd-prod-registry.cn-hangzhou.cr.aliyuncs.com/bpmax-prod-cd/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG

                                imageurl="b-tradd-prod-registry.cn-hangzhou.cr.aliyuncs.com/bpmax-prod-cd/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG"
                            elif [ "${DEPLOYENV}" = "xunjian-test.shuxinyc.com" -o "${DEPLOYENV}" = "yygl-test.shuxinyc.com" ]; then
                                echo "cbdtest registry"
                                docker login -u "bpmax-acr@1605653470292707" -p "4pN@Y79v" b-tradd-test-registry.cn-chengdu.cr.aliyuncs.com
                                docker tag registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}-${lowercase}:$TAG b-tradd-test-registry.cn-chengdu.cr.aliyuncs.com/bpmax-test-cd/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG
                                docker push b-tradd-test-registry.cn-chengdu.cr.aliyuncs.com/bpmax-test-cd/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG

                                imageurl="b-tradd-test-registry.cn-chengdu.cr.aliyuncs.com/bpmax-test-cd/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG"
                            else 
                                echo "bpmax registry"
                                # docker push registry.yitaiyitai.com/bpmax/bpmax:$TAG
                                # docker tag registry.yitaiyitai.com/bpmax/bpmax-${DEPLOYENV}-${lowercase}:$TAG registry.cn-hangzhou.aliyuncs.com/bpmaxpub/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG
                                # docker login --username=xiaobaoj5 -p "QsD&9K}S,J]Z" registry.cn-hangzhou.aliyuncs.com
                                # docker push registry.cn-hangzhou.aliyuncs.com/bpmaxpub/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG

                                imageurl="registry.cn-hangzhou.aliyuncs.com/bpmaxpub/bpmax-${NEW_DEPLOYENV}-${lowercase}:$TAG"
                            fi

                            curl -X GET https://www.feishu.cn/flow/api/trigger-webhook/48bb633af472e560c4826f5e6e87cbdc?env=${DEPLOYENV}&type=BUILD&image=${imageurl}

                        done < ./SERVER_TYPE_ARR_FILE.txt

                    '''
                    
                }

            }
        }

        stage('Make TAG') {
            agent {
                label 'main'
            }

            when {
                expression {
                return env.MAKE_TAG != 'false'
                }
            }

            steps {
                sh "echo ***===*** ${COMMIT_HASH_bpmax_helper}"
                sh "echo ***===*** ${COMMIT_HASH_hpt_frontend}"
                sh "echo ***===*** ${COMMIT_HASH_hpt_mobile}"
                sh "echo ***===*** ${COMMIT_HASH_huanpingtong_server}"
                sh "echo ***===*** ${COMMIT_HASH_bpmax_server}"

                script {
                    if (env.MAKE_TAG.toBoolean()) {

                        sh(script: """
                        curl --request POST --header "PRIVATE-TOKEN: xfJWTsxtJq8Gej39Lz2P" \
                            --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_bpmax_helper}&message=${env.TAG_MSG}" \
                            "https://gitlab.yitaiyitai.com/api/v4/projects/2/repository/tags"
                        """)

                        sh(script: """
                        curl --request POST --header "PRIVATE-TOKEN: 1QHgsxbnGF83mCcqhgW9" \
                            --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_hpt_frontend}&message=${env.TAG_MSG}" \
                            "https://gitlab.yitaiyitai.com/api/v4/projects/8/repository/tags"
                        """)

                        sh(script: """
                        curl --request POST --header "PRIVATE-TOKEN: miW9SobskTNBy2SicojS" \
                            --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_hpt_mobile}&message=${env.TAG_MSG}" \
                            "https://gitlab.yitaiyitai.com/api/v4/projects/9/repository/tags"
                        """)

                        sh(script: """
                        curl --request POST --header "PRIVATE-TOKEN: v87fHeEwWpJXiKesyeSK" \
                            --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_huanpingtong_server}&message=${env.TAG_MSG}" \
                            "https://gitlab.yitaiyitai.com/api/v4/projects/3/repository/tags"
                        """)

                        sh(script: """
                        curl --request POST --header "PRIVATE-TOKEN: o9tXSxvNt15fjniFyC9u" \
                            --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_bpmax_server}&message=${env.TAG_MSG}" \
                            "https://gitlab.yitaiyitai.com/api/v4/projects/7/repository/tags"
                        """)

                    }
                }
            }

        }

    }

}
