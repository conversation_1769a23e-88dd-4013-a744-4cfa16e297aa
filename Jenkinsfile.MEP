pipeline {
  agent none

  environment {
    // BUILD_NUMBER
    COMMIT_HASH_bpmax_helper = ''
    COMMIT_HASH_hpt_frontend = ''
    COMMIT_HASH_hpt_mobile = ''
    COMMIT_HASH_huanpingtong_server = ''
    COMMIT_HASH_bpmax_server = ''
    DEPLOY_SERVER_ip = ''
    DEPLOY_SERVER_port = ''
    DEPLOY_SERVER_domain = ''
    DEPLOY_SERVER_ip_origin = ''
    // CREATE、UPDATE、CREATE_DELETE
    DEPLOY_type = ''
    DEPLOY_CACHE_file = '/var/jenkins_home/deploy-records.data'
    SERVERS_CACHE_file = './servers/server_alias.data'
  }

  stages {

    stage('Set Name') {
      agent {
        // 使用标签为ssh的agent
        label 'main'
      }
      steps {
        sh '''
        id
        echo ${SERVER_ALIAS}
        echo ${DEPLOYENV}
        cat ${SERVERS_CACHE_file}
        cat ${DEPLOY_CACHE_file}

        var_file_path=./tmpdata/${BUILD_NUMBER}/
        rm -rf "$var_file_path"
        mkdir -p "$var_file_path"
        chmod 777 -R "$var_file_path"
        echo "======BUILD_NUMBER: ${BUILD_NUMBER} ======"
        touch "${var_file_path}/DEPLOY_summary"

        if [ "${OFFLINE}" = "false" ]; then
          set +ex
          deploy_record="$(cat "$DEPLOY_CACHE_file" | grep "$DEPLOYENV")"

          if [ -z $deploy_record ]; then
            echo "====== deploy record is nil ======"

            if [ "$SERVER_ALIAS" = 'nil' ]; then
              echo "====== SERVER_ALIAS is nil, deploy fail!!! ======"
            else
              echo ====== SERVER_ALIAS: $SERVER_ALIAS ======
              DEPLOY_SERVER_ip=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" | awk -F"=" '{print $2}')
              echo ====== DEPLOY_SERVER_ip: $DEPLOY_SERVER_ip ======
              DEPLOY_type="CREATE"
              DEPLOY_SERVER_domain=$DEPLOYENV
            fi

          else
            echo "====== deploy record exist ======"
            DEPLOY_SERVER_ip=$(echo $deploy_record | awk -F' ' '{print $1}')
            DEPLOY_SERVER_port=$(echo $deploy_record | awk -F' ' '{print $2}')
            DEPLOY_SERVER_domain=${DEPLOYENV}

            if [ "$SERVER_ALIAS" = 'nil' ]; then

              echo "====== SERVER_ALIAS is nil, deploy update!!! ======"
              DEPLOY_type="UPDATE"

            else

              echo ====== SERVER_ALIAS exist: $SERVER_ALIAS, deploy create and delete!!! ======
              DEPLOY_SERVER_ip_origin=$DEPLOY_SERVER_ip
              DEPLOY_SERVER_ip=$(cat $SERVERS_CACHE_file | grep "$SERVER_ALIAS" | awk -F"=" '{print $2}')
              echo ====== DEPLOY_SERVER_ip_origin: $DEPLOY_SERVER_ip_origin ======
              DEPLOY_type="CREATE_DELETE"

              if [ "$DEPLOY_SERVER_ip_origin" = "$DEPLOY_SERVER_ip" ]; then
                echo "====== IP addresses are the same ======"
                DEPLOY_type="UPDATE"
              fi
              
            fi

          fi

          probe_port() {
            for i in $(seq 24333 24350); do
              echo "=== probe $1 $i ==="
              result=$(telnet $1 $i)
              r=$(echo $result | grep -v "Escape character")
              if [ -n "$r" ]; then
                DEPLOY_SERVER_port=$i
                break;
              fi
            done
          }

          if [ "$DEPLOY_type" = "CREATE" -o "$DEPLOY_type" = "CREATE_DELETE" ]; then
            echo "====== create port ======"

            echo $DEPLOY_SERVER_ip | grep '192.168.31.'
            if [ $? -eq 0 ]; then
              probe_port $DEPLOY_SERVER_ip
            else
              DEPLOY_SERVER_port="FILE_FREEZE"
            fi
            
          fi

          echo "====== check important vars: ip $DEPLOY_SERVER_ip, port $DEPLOY_SERVER_port, domain $DEPLOY_SERVER_domain, ip_origin $DEPLOY_SERVER_ip_origin, type $DEPLOY_type ======" >> "${var_file_path}/DEPLOY_summary"

          create_var_file() {
            if [ -z "$3" ]; then
              echo "val is nil"
              return 0
            fi
            filename="${1}${2}"
            touch "$filename"
            echo $3 > "$filename"
          }

          create_var_file $var_file_path DEPLOY_type $DEPLOY_type
          
          create_var_file $var_file_path DEPLOY_SERVER_ip $DEPLOY_SERVER_ip

          create_var_file $var_file_path DEPLOY_SERVER_port $DEPLOY_SERVER_port

          create_var_file $var_file_path DEPLOY_SERVER_domain $DEPLOY_SERVER_domain

          create_var_file $var_file_path DEPLOY_SERVER_ip_origin $DEPLOY_SERVER_ip_origin

        fi
        '''
        script {
            currentBuild.description = "BuildDocker: ${BUILDDOCKER} \nDeployEnv: ${DEPLOYENV} \n" + 
              "SERVER_ALIAS: ${SERVER_ALIAS} \n" +
              "bpmax-helper: ${branch_bpmax_helper} \n" + 
              "hpt-frontend: ${branch_hpt_frontend} \n" + 
              "hpt-mobile: ${branch_hpt_mobile} \n" + 
              "huanpingtong-server: ${branch_huanpingtong_server} \n" + 
              "bpmax-server: ${branch_bpmax_server} \n"
        }

        script {
            deploy_type="./tmpdata/${BUILD_NUMBER}/DEPLOY_type"
            if(fileExists(deploy_type)) {
              def fileContent = readFile(deploy_type)
              echo "继续部署流程...进入下一个阶段"
            } else {
              // sh(script: "echo '缺少部署服务器，终止部署' >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary")
              // error "缺少部署服务器，终止部署"
              sh'''
                if [ "${OFFLINE}" = "true" ]; then
                  echo '离线部署' >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                else
                  echo '非离线部署，缺少部署服务器，终止部署' >> ./tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                fi
              '''
            }
        }

      }
      
    }


// 测试阶段关闭
    stage('Build Project') {
      parallel {

        stage('Check bpmax-helper') {
          agent {
            docker { 
              label 'main'
              image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
              args '-v /yarn_cache_mep_1:/usr/local/share/.cache/yarn -u 0:0'
              reuseNode true
            }
          }
          environment {
            API_HOST = '/api'
            JOB_PWD = ''
          }
          steps {
            dir('bpmax-helper') {
              script {
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                sh '''
                  if [ -d ".git" ]; then
                    echo "Git repository exists, cleaning up..."
                    git fetch --prune origin
                    git tag -l | xargs git tag -d
                  else
                    echo "No Git repository found, skipping cleanup"
                  fi
                '''
                if (env.BRANCH_TYPE_bpmax_helper.toBoolean()) {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/heads/${branch_bpmax_helper}"]], 
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                } else {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/tags/${branch_bpmax_helper}"]],
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                }

                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                COMMIT_HASH_bpmax_helper = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                echo "bpmax_helper hash"
                echo COMMIT_HASH_bpmax_helper
              }
            }
          }
        }

        stage('Build hpt-frontend') {
          agent {
            docker { 
              label 'runner'
              image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
              args '-v /yarn_cache_mep_1:/usr/local/share/.cache/yarn -u 0:0'
              reuseNode true
            }
          }
          environment {
            API_HOST = '/api'
            JOB_PWD = ''
          }
          steps {
            sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'
            sh '''
              if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                rm -rf /usr/local/share/.cache/yarn/v6/.tmp
              else
                echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
              fi
            '''
            
            dir('hpt-frontend') {
              script {
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                sh '''
                  if [ -d ".git" ]; then
                    echo "Git repository exists, cleaning up..."
                    git fetch --prune origin
                    git tag -l | xargs git tag -d
                  else
                    echo "No Git repository found, skipping cleanup"
                  fi
                '''
                if (env.BRANCH_TYPE_hpt_frontend.toBoolean()) {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/heads/${branch_hpt_frontend}"]], 
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                } else {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/tags/${branch_hpt_frontend}"]],
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                }

                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                COMMIT_HASH_hpt_frontend = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                echo "hpt_frontend hash"
                echo COMMIT_HASH_hpt_frontend
              }
              sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
              sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false && yarn --registry="https://npm.yitaiyitai.com/" --no-cache'
              sh 'rm -rf node_modules/@bpmax/helper && git clone -b ${branch_bpmax_helper} http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper'
              sh '''
                export $(xargs < ../envs/.env_${DEPLOYENV})
                if [ -n "$PUBLICPATH_PC" ]; then
                  export PUBLIC_PATH=https://$OSSBUCKET.$OSSREEGION.aliyuncs.com$PUBLICPATH_PC
                fi
                rm -rf .nuxt dist ../dist_pre && yarn build
                mkdir -p ../dist_pre/hpt-frontend && rm -rf ../dist_pre/hpt-frontend/*
                cp -r .nuxt dist static utils nuxt.config.js package.json yarn.lock ../dist_pre/hpt-frontend/
                chown -R 1000:1000 ../dist_pre
                echo $PUBLICPATH_PC
                if [ -n "$PUBLICPATH_PC" ]; then
                  curl https://gosspublic.alicdn.com/ossutil/install.sh | bash
                  ossutil cp -r -f dist oss://$OSSBUCKET$PUBLICPATH_PC --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --include "*.js" --meta Content-Type:application/javascript
                  ossutil cp -r -f dist oss://$OSSBUCKET$PUBLICPATH_PC --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --exclude "*.js"
                fi
                rm -rf node_modules/.cache
              '''
            }

            // dir('dist_pre/hpt-frontend') {
            //    sh "sed -i '/woshiwu/d' package.json"
            // }

            script {
              def stashName = "${env.JOB_NAME}_hpt-frontend"
              stash name: stashName, includes: 'dist_pre/hpt-frontend/**/*'
            }
          }
        }

        stage('Build hpt-mobile') {
          agent {
            docker { 
              label 'runner'
              image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
              reuseNode true
              args '-v /yarn_cache_mep_2:/usr/local/share/.cache/yarn -u 0:0'
            }
          }
          environment {
            API_HOST = '/api'
            JOB_PWD = ''
          }
          steps {
            sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'
            sh '''
              if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                rm -rf /usr/local/share/.cache/yarn/v6/.tmp
              else
                echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
              fi
            '''
            
            dir('hpt-mobile') {
              script {
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                sh '''
                  if [ -d ".git" ]; then
                    echo "Git repository exists, cleaning up..."
                    git fetch --prune origin
                    git tag -l | xargs git tag -d
                  else
                    echo "No Git repository found, skipping cleanup"
                  fi
                '''
                if (env.BRANCH_TYPE_hpt_mobile.toBoolean()) {
                  checkout(
                    [$class: 'GitSCM', branches: [[name: "refs/heads/${branch_hpt_mobile}"]], 
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-mobile.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])                    

                } else {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/tags/${branch_hpt_mobile}"]],
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/hpt-mobile.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                }
                sh '''
                  echo "===> 缓存目录"
                  pwd
                '''

                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                COMMIT_HASH_hpt_mobile = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
              }

              sh '''
                git config --global user.email "<EMAIL>"
                git config --global user.name "jenkins"
              '''
              sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
              sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false && yarn --registry="https://npm.yitaiyitai.com/" --no-cache'
              sh "yarn remove huanpingtong && yarn add huanpingtong@git+http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git#${branch_hpt_frontend} --skip-integrity-check --no-lockfile --force --no-save"
              sh '''
                export $(xargs < ../envs/.env_${DEPLOYENV})
                if [ -n "$PUBLICPATH_MOBILE" ]; then
                  export PUBLIC_PATH=https://$OSSBUCKET.$OSSREEGION.aliyuncs.com$PUBLICPATH_MOBILE
                fi
                rm -rf .nuxt dist ../dist_pre && yarn generate
                mkdir -p ../dist_pre/hpt-mobile && rm -rf ../dist_pre/hpt-mobile/*
                cp -r dist ../dist_pre/hpt-mobile/
                chown -R 1000:1000 ../dist_pre
                echo $PUBLICPATH_MOBILE
                if [ -n "$PUBLICPATH_MOBILE" ]; then
                  curl https://gosspublic.alicdn.com/ossutil/install.sh | bash
                  ossutil cp -r -f dist/build/m oss://$OSSBUCKET$PUBLICPATH_MOBILE --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --include "*.js" --meta Content-Type:application/javascript
                  ossutil cp -r -f dist/build/m oss://$OSSBUCKET$PUBLICPATH_MOBILE --endpoint $OSSREEGION.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read --exclude "*.js"
                fi
                rm -rf node_modules/.cache
              '''
            }
            script {
              def stashName = "${env.JOB_NAME}_hpt-mobile"
              stash name: stashName, includes: 'dist_pre/hpt-mobile/**/*'
            }
          }
        }

        stage('Build huanpingtong-server') {
          agent {
            docker { 
              label 'runner || main'
              image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
              reuseNode true
              args '-v /yarn_cache_mep_3:/usr/local/share/.cache/yarn -u 0:0'
            }
          }
          environment {
            API_HOST = '/api'
            JOB_PWD = ''
          }
          steps {
            sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'

            sh '''
              if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                rm -rf /usr/local/share/.cache/yarn/v6/.tmp
              else
                echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
              fi
            '''
            
            dir('huanpingtong-server') {
              script {
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                sh '''
                  if [ -d ".git" ]; then
                    echo "Git repository exists, cleaning up..."
                    git fetch --prune origin
                    git tag -l | xargs git tag -d
                  else
                    echo "No Git repository found, skipping cleanup"
                  fi
                '''
                if (env.BRANCH_TYPE_huanpingtong_server.toBoolean()) {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/heads/${branch_huanpingtong_server}"]], 
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/huanpingtong-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                } else {

                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/tags/${branch_huanpingtong_server}"]],
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/huanpingtong-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                }
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                COMMIT_HASH_huanpingtong_server = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
              }
              sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
              sh "cat yarn.lock | grep 'https://registry.npmmirror.com' | wc -l"
              sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false'
              sh 'rm -rf node_modules && yarn add ssh2@1.4.0 --ignore-optional && yarn --registry="https://npm.yitaiyitai.com/" --frozen-lockfile --no-cache'
              sh "yarn remove huanpingtong && yarn add huanpingtong@git+http://gitlabbot:<EMAIL>/bpmax/hpt-frontend.git#${branch_hpt_frontend} --skip-integrity-check --no-lockfile --force --no-save"
              sh 'rm -rf node_modules/@bpmax/helper && git clone -b ${branch_bpmax_helper} http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper'
              sh 'rm -rf app ../dist_pre && yarn compile'
              sh 'mkdir -p ../dist_pre/huanpingtong-server && rm -rf ../dist_pre/huanpingtong-server/*'
              sh 'cp -r app config custom migrations view www nuxt.config.admin.js .db-migraterc package.json production.js yarn.lock ../dist_pre/huanpingtong-server/; chown -R 1000:1000 ../dist_pre'
              sh 'rm -rf node_modules/.cache'
            }

            script {
              def stashName = "${env.JOB_NAME}_huanpingtong-server"
              stash name: stashName, includes: 'dist_pre/huanpingtong-server/**/*'
            }
          }
        }

        stage('Build bpmax-server') {
          agent {
            docker { 
              label 'runner'
              image 'registry.cn-shanghai.aliyuncs.com/bpmax/public:bpmax-base-16.20.2-2.0' 
              reuseNode true
              args '-v /yarn_cache_mep_4:/usr/local/share/.cache/yarn -u 0:0'
            }
          }
          environment {
            API_HOST = '/api'
            JOB_PWD = ''
          }
          steps {
            sh 'echo "************** gitlab.yitaiyitai.com" >> /etc/hosts'
            
            sh '''
              if [ -d "/usr/local/share/.cache/yarn/v6/.tmp" ]; then
                ls -lha /usr/local/share/.cache/yarn/v6/.tmp
                rm -rf /usr/local/share/.cache/yarn/v6/.tmp
              else
                echo "不存在 /usr/local/share/.cache/yarn/v6/.tmp"
              fi
            '''

            dir('bpmax-server') {
              script {
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                sh '''
                  if [ -d ".git" ]; then
                    echo "Git repository exists, cleaning up..."
                    git fetch --prune origin
                    git tag -l | xargs git tag -d
                  else
                    echo "No Git repository found, skipping cleanup"
                  fi
                '''
                if (env.BRANCH_TYPE_bpmax_server.toBoolean()) {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/heads/${branch_bpmax_server}"]], 
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                } else {
                  checkout([
                    $class: 'GitSCM', 
                    branches: [[name: "refs/tags/${branch_bpmax_server}"]],
                    userRemoteConfigs: [
                      [url: 'http://gitlabbot:<EMAIL>/bpmax/bpmax-server.git', credentialsId: 'f75a9928-9b05-4dd7-8fdf-57d80e312ddf']
                    ]
                  ])
                }
                JOB_PWD = sh(script: 'pwd', returnStdout: true).trim()
                sh "git config --global --add safe.directory ${JOB_PWD}"
                COMMIT_HASH_bpmax_server = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
              }
              sh "sed -i 's|https://registry.npmmirror.com|https://npm.yitaiyitai.com|g' yarn.lock"
              sh 'yarn config set registry "https://npm.yitaiyitai.com" && yarn config set enable-audit false && yarn config set strict-ssl false && yarn --registry="https://npm.yitaiyitai.com/" --no-cache'
              sh 'echo ==-${branch_bpmax_helper} && rm -rf node_modules/@bpmax/helper && git clone -b ${branch_bpmax_helper} http://gitlabbot:<EMAIL>/bpmax/bpmax-helper.git node_modules/@bpmax/helper'
              sh 'rm -rf dist ../dist_pre && yarn build'
              sh 'mkdir -p ../dist_pre/bpmax-server && rm -rf ../dist_pre/bpmax-server/*'
              sh 'cp -r dist package.json yarn.lock ../dist_pre/bpmax-server/; chown -R 1000:1000 ../dist_pre'
              sh 'rm -rf node_modules/.cache'
            }
            
            script {
              def stashName = "${env.JOB_NAME}_bpmax-server"
              stash name: stashName, includes: 'dist_pre/bpmax-server/**/*'
            }
            
          }
        }

      }
    }

    stage('Build Prod Package') {
      agent {
        // 使用标签为ssh的agent
        label 'main'
      }
      steps {
        dir('dist_prod') {
          sh 'id'
          sh 'pwd && rm -rf hpt-frontend hpt-mobile huanpingtong-server bpmax-server'
          sh "ls -l"
        }
        sh 'echo $JOB_NAME=============JOB_NAMEJOB_NAMEJOB_NAME============'
        unstash "${env.JOB_NAME}_hpt-frontend"
        unstash "${env.JOB_NAME}_hpt-mobile"
        unstash "${env.JOB_NAME}_huanpingtong-server"
        unstash "${env.JOB_NAME}_bpmax-server"

        sh "find dist_pre -regex '.*\\.map' -print0 | xargs -0 rm -f && mv dist_pre/* dist_prod/"
        
        dir('docker') {
          sh '''
          if [ -f "bpmax_prod.tar" ]; then
              rm -rf bpmax_prod.tar
          fi
          '''
          sh 'mkdir -p ../dist'
          
          sh '''
          # 如果 $BUILDDOCKER 等于 "true" 则执行
          if [ "$BUILDDOCKER" = "true" ]; then
            tryfile=`curl -I -s -o /dev/null -w "%{http_code}\n" https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_runner/crun-$DEPLOYENV | grep '200' | wc -l`
            echo -=-=+$tryfile
            TAG=`date +%Y%m%d%H%M`
            if [ "$tryfile" -eq 1 ]; then
              echo "tryfile case $tryfile"
              sed -i "s/crun-all/crun-$DEPLOYENV/g" Dockerfile
            fi
            echo "Dockerfile======>"
            cat ./Dockerfile
            echo "<==="
            set +ex
            docker rmi registry.yitaiyitai.com/bpmax/bpmax:$TAG

            ls -l filebeat/

            set -ex
            cp filebeat/filebeat.yml filebeat/filebeat-copy.yml
            sed -i "s/{{UniqueID}}/$DEPLOYENV/g" filebeat/filebeat-copy.yml
            rm -rf filebeat/filebeat.yml
            mv filebeat/filebeat-copy.yml filebeat/filebeat.yml

            docker build --no-cache -t registry.yitaiyitai.com/bpmax/bpmax:$TAG .
            sed -i "s/{{TAG}}/$TAG/g" ../docker-compose-release.yml
            sed -i "s/{{TAG}}/$TAG/g" ../deploy-op.sh
            docker save -o bpmax_prod.tar registry.yitaiyitai.com/bpmax/bpmax:$TAG

          # 否则, 删除bpmax_prod.tar
          else
            # rm -rf bpmax_prod.tar
            echo "without build image"
          fi

          t_logger=`date "+%Y-%m-%d"`
          if [ "$LOGGER" = "true" ]; then
            sed -i "s%{{LOGGER}}%>> /var/log/clogs/clog-${t_logger}.log 2>\\&1%g" ../docker-compose-release.yml
          else
            sed -i "s/{{LOGGER}}/ /g" ../docker-compose-release.yml
          fi
          sed -i "s%{{clog.log}}%clog-${t_logger}.log%g" ../docker-compose-release.yml

          cp ../docker-compose-release.yml ../docker-compose-release-new.yaml
          cp ../deploy-op.sh ../deploy-op-new.sh

          '''
          sh '''
          if [ -f ../dist/files.tar.gz ]; then
              rm -rf ../dist/files.tar.gz
          fi
          '''
          
          sh 'tar -czf ../dist/files.tar.gz ./* ../dist_prod/*'
        }
      }
    }
// 测试阶段关闭

    stage('Deploy Prod') {
      agent {
        // 主节点
        label 'main'
      }
      // when {
      //   expression {
      //     return env.OFFLINE != 'true'
      //   }
      // }

      steps {
        
        dir('dist') {

          script {
            DEPLOY_SERVER_domain_file = "../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_domain"
            if(fileExists(DEPLOY_SERVER_domain_file)) {
              DEPLOY_SERVER_domain = readFile(DEPLOY_SERVER_domain_file)
              echo DEPLOY_SERVER_domain_file
            }

            DEPLOY_HASH_bpmax_helper = "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_bpmax_helper"
            DEPLOY_HASH_hpt_frontend = "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_hpt_frontend"
            DEPLOY_HASH_hpt_mobile = "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_hpt_mobile"
            DEPLOY_HASH_huanpingtong_server = "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_huanpingtong_server"
            DEPLOY_HASH_bpmax_server = "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_bpmax_server"

            sh "echo ${COMMIT_HASH_bpmax_helper} > ${DEPLOY_HASH_bpmax_helper}"
            sh "echo ${COMMIT_HASH_hpt_frontend} > ${DEPLOY_HASH_hpt_frontend}"
            sh "echo ${COMMIT_HASH_hpt_mobile} > ${DEPLOY_HASH_hpt_mobile}"
            sh "echo ${COMMIT_HASH_huanpingtong_server} > ${DEPLOY_HASH_huanpingtong_server}"
            sh "echo ${COMMIT_HASH_bpmax_server} > ${DEPLOY_HASH_bpmax_server}"

            echo "build hash"
            echo COMMIT_HASH_bpmax_helper
            echo COMMIT_HASH_hpt_frontend
            echo COMMIT_HASH_hpt_mobile
            echo COMMIT_HASH_huanpingtong_server
            echo COMMIT_HASH_bpmax_server
          }

          sh '''
          if [ "${OFFLINE}" = "false" ]; then
            DEPLOY_SERVER_domain=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_domain"`
            DEPLOY_SERVER_port=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_port"`

            if [ $DEPLOY_SERVER_port != "FILE_FREEZE" ]; then
              echo $DEPLOY_SERVER_domain----------
              sed -i '/port=/d' ../envs/.env_${DEPLOY_SERVER_domain}
              echo "port=${DEPLOY_SERVER_port}" >> ../envs/.env_${DEPLOY_SERVER_domain}
            fi
            
          fi

          cp ../envs/.env_${DEPLOYENV} ./.env
          cp ../docker-compose-release-new.yaml ./docker-compose.yml
          sed -i "s#helperBranch='master'#helperBranch=\"${branch_bpmax_helper}\"#g" ../deploy-op-new.sh
          echo "ECHO=HELPER_BRANCH=${branch_bpmax_helper}"
          cp ../deploy-op-new.sh ./deploy.sh
          
          '''
        }

        dir('release') {
          sh '''

          tar -czf ./release.tar.gz ../dist/.env ../dist/* 

          DEPLOY_HASH_bpmax_helper=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_bpmax_helper"`
          DEPLOY_HASH_hpt_frontend=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_hpt_frontend"`
          DEPLOY_HASH_hpt_mobile=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_hpt_mobile"`
          DEPLOY_HASH_huanpingtong_server=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_huanpingtong_server"`
          DEPLOY_HASH_bpmax_server=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_HASH_bpmax_server"`

          if [ "${OFFLINE}" = "false" ]; then
            set +ex
            DEPLOY_SERVER_domain=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_domain"`
            DEPLOY_SERVER_port=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_port"`
            DEPLOY_SERVER_ip=`cat "../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip"`
            DEPLOY_type=`cat ../tmpdata/${BUILD_NUMBER}/DEPLOY_type`
            DEPLOY_SERVER_ip_origin=`cat ../tmpdata/${BUILD_NUMBER}/DEPLOY_SERVER_ip_origin`

            # 拷贝
            scp -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" ./release.tar.gz root@${DEPLOY_SERVER_ip}:/root/
            # 执行
            DEPLOYENV_TO_DEPLOYDIR=$(echo $DEPLOYENV | sed 's/\\./_/g')
            ssh -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} "mkdir -p /root/app/${DEPLOYENV_TO_DEPLOYDIR} && mkdir -p /root/tmpapp/ && mv /root/release.tar.gz /root/tmpapp/ && cd /root/tmpapp && tar zxf release.tar.gz && cd dist && sh deploy.sh ${branch_bpmax_helper}"
            echo "====== 应用部署完成 ======" >> ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary
            # 清理临时目录
            ssh -i "${HOME}/.ssh/${DEPLOY_SERVER_ip}.pem" root@${DEPLOY_SERVER_ip} "rm -rf /root/tmpapp"
            echo "====== 应用部署临时目录清理完成 ======" >> ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary
            
            echo $DEPLOY_SERVER_ip | grep '192.168.31.'
            if [ $? -eq 0 ]; then
              # 部署转发配置
              if [ $DEPLOY_type = 'CREATE' -o $DEPLOY_type = 'CREATE_DELETE' ]; then
                ngtpl_file='../servers/nginx.conf.tpl'
                sed -i "s/{{ip}}/$DEPLOY_SERVER_ip/g" $ngtpl_file
                sed -i "s/{{port}}/$DEPLOY_SERVER_port/g" $ngtpl_file
                sed -i "s/{{domain}}/$DEPLOY_SERVER_domain/g" $ngtpl_file

                cat $ngtpl_file
                mv ../servers/nginx.conf.tpl "../servers/${DEPLOY_SERVER_domain}.conf"
                scp -i "${HOME}/.ssh/**************.pem" ../servers/${DEPLOY_SERVER_domain}.conf root@**************:/etc/nginx/conf.d/
                ssh -i "${HOME}/.ssh/**************.pem" root@************** "nginx -t"
                if [ $? -eq 0 ]; then
                  # 重启服务
                  ssh -i "${HOME}/.ssh/**************.pem" root@************** "nginx -s reload"
                  echo "====== 重启转发服务 ======" >> ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                else
                  echo "====== FAIL: 转发服务配置存在问题!!! ======" >> ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                fi
              fi

              # CREATE_DELETE, 清理服务, docker compose down
              set +ex
              if [ $DEPLOY_type = 'CREATE_DELETE' ]; then
                ssh -i "${HOME}/.ssh/${DEPLOY_SERVER_ip_origin}.pem" root@${DEPLOY_SERVER_ip_origin} "cd /root/app/${DEPLOYENV_TO_DEPLOYDIR} && docker compose down"
                if [ $? -eq 0 ]; then
                  echo "====== 清理服务完成 ======" >> ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                else
                  echo "====== 清理服务失败 ======" >> ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary
                fi
              fi

            fi

            # 清理历史记录
            sed -i "/$DEPLOY_SERVER_domain/d" "$DEPLOY_CACHE_file"
            
            # 写入 deploy-records.data
            # **************  FILE_FREEZE  cicada.bpmax.cn  2024-09-28T14:34:18CST
            echo "$DEPLOY_SERVER_ip  $DEPLOY_SERVER_port  $DEPLOY_SERVER_domain  $(TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S%Z")  bpmax_helper::$DEPLOY_HASH_bpmax_helper  hpt_frontend::$DEPLOY_HASH_hpt_frontend  hpt_mobile::$DEPLOY_HASH_hpt_mobile  huanpingtong_server::$DEPLOY_HASH_huanpingtong_server  bpmax_server::$DEPLOY_HASH_bpmax_server" >> "$DEPLOY_CACHE_file"
            
            cat ../tmpdata/${BUILD_NUMBER}/DEPLOY_summary

            echo "====== 清理压缩包 ======"
            rm -f ./release.tar.gz
          
          else

            # 清理历史记录
            sed -i "/$DEPLOYENV/d" "$DEPLOY_CACHE_file"

            # 写入打包记录
            echo "OFFLINE  FILE_FREEZE  $DEPLOYENV  $(TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S%Z")  bpmax_helper::$DEPLOY_HASH_bpmax_helper  hpt_frontend::$DEPLOY_HASH_hpt_frontend  hpt_mobile::$DEPLOY_HASH_hpt_mobile  huanpingtong_server::$DEPLOY_HASH_huanpingtong_server  bpmax_server::$DEPLOY_HASH_bpmax_server" >> "$DEPLOY_CACHE_file"

            mv ./release.tar.gz ./release-${DEPLOYENV}.tar.gz

            # https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_pkg/release-${DEPLOYENV}-`date +%Y%m%dT%H%M%S`.tar.gz
            curl -O https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_pkg/ossutil64
            chmod 777 ./ossutil64
            export $(xargs < ../envs/.env_zbuilder)
            # 清理OSS
            ./ossutil64 ls oss://pw-garden/bpmaxapp_pkg/ --endpoint oss-cn-shanghai.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET
            ./ossutil64 rm -rf oss://pw-garden/bpmaxapp_pkg/ --include "release-${DEPLOYENV}*" --endpoint oss-cn-shanghai.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET

            # 上传OSS
            time=`date +%Y%m%dT%H%M%S`
            ./ossutil64 cp -f release-${DEPLOYENV}.tar.gz oss://pw-garden/bpmaxapp_pkg/release-${DEPLOYENV}-${time}.tar.gz --endpoint oss-cn-shanghai.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET --acl public-read
            ./ossutil64 ls oss://pw-garden/bpmaxapp_pkg/ --endpoint oss-cn-shanghai.aliyuncs.com -i $ALIACCESSKEYID -k $ALIACCESSKEYSECRET
            echo "OFFLINE-TAR: https://pw-garden.oss-cn-shanghai.aliyuncs.com/bpmaxapp_pkg/release-${DEPLOYENV}-${time}.tar.gz"
          fi

          curl -X GET https://www.feishu.cn/flow/api/trigger-webhook/48bb633af472e560c4826f5e6e87cbdc?env=${DEPLOYENV}&type=DEPLOY
          '''

          // script{
          //   // archiveArtifacts artifacts: "release-${DEPLOYENV}.tar.gz", onlyIfSuccessful: true
          //   def artifactName = "release-${DEPLOYENV}.tar.gz"
          //   if (fileExists(artifactName)) {
          //       echo "File exists, proceeding to archive."
          //       archiveArtifacts artifacts: artifactName, onlyIfSuccessful: true
          //   } else {
          //       echo "File does not exist, skipping archive."
          //   }
          // }
        }

      }
    }

    stage('Make TAG') {
      agent {
        label 'main'
      }

      when {
        expression {
          return env.MAKE_TAG != 'false'
        }
      }

      steps {
        sh "echo ***===*** ${COMMIT_HASH_bpmax_helper}"
        sh "echo ***===*** ${COMMIT_HASH_hpt_frontend}"
        sh "echo ***===*** ${COMMIT_HASH_hpt_mobile}"
        sh "echo ***===*** ${COMMIT_HASH_huanpingtong_server}"
        sh "echo ***===*** ${COMMIT_HASH_bpmax_server}"

        script {
          if (env.MAKE_TAG.toBoolean()) {

            sh(script: """
              curl --request POST --header "PRIVATE-TOKEN: xfJWTsxtJq8Gej39Lz2P" \
                --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_bpmax_helper}&message=${env.TAG_MSG}" \
                "https://gitlab.yitaiyitai.com/api/v4/projects/2/repository/tags"
            """)

            sh(script: """
              curl --request POST --header "PRIVATE-TOKEN: 1QHgsxbnGF83mCcqhgW9" \
                --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_hpt_frontend}&message=${env.TAG_MSG}" \
                "https://gitlab.yitaiyitai.com/api/v4/projects/8/repository/tags"
            """)

            sh(script: """
              curl --request POST --header "PRIVATE-TOKEN: miW9SobskTNBy2SicojS" \
                --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_hpt_mobile}&message=${env.TAG_MSG}" \
                "https://gitlab.yitaiyitai.com/api/v4/projects/9/repository/tags"
            """)

            sh(script: """
              curl --request POST --header "PRIVATE-TOKEN: v87fHeEwWpJXiKesyeSK" \
                --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_huanpingtong_server}&message=${env.TAG_MSG}" \
                "https://gitlab.yitaiyitai.com/api/v4/projects/3/repository/tags"
            """)

            sh(script: """
              curl --request POST --header "PRIVATE-TOKEN: o9tXSxvNt15fjniFyC9u" \
                --data "tag_name=${env.TAG_MSG}&ref=${COMMIT_HASH_bpmax_server}&message=${env.TAG_MSG}" \
                "https://gitlab.yitaiyitai.com/api/v4/projects/7/repository/tags"
            """)

          }
        }
      }

    }

  }

}
