pipeline {
    agent {
        label 'main'
    }

    parameters {
        choice(
            name: 'OperationType',
            choices: ['OnlyPlatformUpdate', 'BusinessPluginsCreateAndUpdate'],
            description: '操作类型：OnlyPlatformUpdate(仅平台更新) / BusinessPluginsCreateAndUpdate(业务插件创建和更新)'
        )
        string(
            name: 'BaseImage',
            defaultValue: '',
            description: '新平台镜像地址，使用AIO pipeline构建出的镜像（完整镜像地址）'
        )
        string(
            name: 'SrcImage',
            defaultValue: '',
            description: '上一个业务镜像地址（完整镜像地址）'
        )
        string(
            name: 'DestImageName',
            defaultValue: '',
            description: '新生成业务镜像地址（完整镜像地址）'
        )
        choice(
            name: 'RegistryEnv',
            choices: ['pre', 'prod'],
            description: '镜像注册的环境: pre=>测试环境 / prod=>正式环境'
        )
        choice(
            name: 'Business',
            choices: ['xunjian', 'xunjian-peixun', 'xunjian-ai', 'xunjian-ai-peixun'],
            description: '业务名称（BusinessPluginsCreateAndUpdate模式时必选）'
        )
        string(
            name: 'BusinessName',
            defaultValue: '',
            description: '业务镜像的可识别名称（用于服务注册显示）'
        )
    }

    environment {
        REMOTE_HOST = '**************'
        SSH_KEY = "${HOME}/.ssh/saas-build.pem"
        WORK_DIR = '/user/app/saas-images'
        REPO_URL = 'http://oauth:<EMAIL>/bpmax/bpmax-globaldata.git'
    }

    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    echo "=== 参数验证 ==="
                    echo "OperationType: ${params.OperationType}"
                    echo "BaseImage: ${params.BaseImage}"
                    echo "SrcImage: ${params.SrcImage}"
                    echo "DestImageName: ${params.DestImageName}"
                    echo "Business: ${params.Business}"
                    echo "BusinessName: ${params.BusinessName}"

                    // 验证必填参数
                    if (!params.BaseImage?.trim()) {
                        error("BaseImage 参数不能为空")
                    }
                    if (!params.SrcImage?.trim()) {
                        error("SrcImage 参数不能为空")
                    }
                    if (!params.DestImageName?.trim()) {
                        error("DestImageName 参数不能为空")
                    }
                    if (!params.Business?.trim()) {
                        error("Business 参数不能为空")
                    }
                    if (!params.BusinessName?.trim()) {
                        error("BusinessName 参数不能为空")
                    }

                    // 验证镜像地址格式（基本检查）
                    def imagePattern = /^[a-zA-Z0-9._\-\/]+:[a-zA-Z0-9._\-]+$/
                    if (!params.BaseImage.matches(imagePattern)) {
                        error("BaseImage 格式不正确，应为 registry/image:tag 格式")
                    }
                    if (!params.SrcImage.matches(imagePattern)) {
                        error("SrcImage 格式不正确，应为 registry/image:tag 格式")
                    }
                    if (!params.DestImageName.matches(imagePattern)) {
                        error("DestImageName 格式不正确，应为 registry/image:tag 格式")
                    }

                    echo "✅ 参数验证通过"
                }
            }
        }

        stage('Prepare Environment') {
            steps {
                script {
                    echo "=== 准备远程环境 ==="
                    sh """
                        echo "检查SSH连接..."
                        ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} "echo 'SSH连接成功'"

                        echo "创建工作目录..."
                        ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} "mkdir -p ${WORK_DIR}"
                    """
                }
            }
        }

        stage('Sync Repository') {
            steps {
                script {
                    echo "=== 同步代码仓库 ==="
                    sh """
                        ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                            set -euo pipefail  # 启用严格模式

                            # 验证环境变量
                            if [ -z "${WORK_DIR}" ]; then
                                echo "错误: WORK_DIR 变量未设置"
                                exit 1
                            fi

                            if [ -z "${REPO_URL}" ]; then
                                echo "错误: REPO_URL 变量未设置"
                                exit 1
                            fi

                            echo "工作目录: ${WORK_DIR}"
                            echo "仓库地址: ${REPO_URL}"

                            # 安全地切换到工作目录
                            cd "${WORK_DIR}" || { echo "错误: 无法切换到工作目录 ${WORK_DIR}"; exit 1; }

                            if [ -d "bpmax-globaldata" ]; then
                                echo "bpmax-globaldata 目录已存在，执行 git pull..."
                                cd bpmax-globaldata || { echo "错误: 无法进入 bpmax-globaldata 目录"; exit 1; }
                                git pull origin master || { echo "错误: git pull 失败"; exit 1; }
                            else
                                echo "bpmax-globaldata 目录不存在，执行 git clone..."
                                git clone "${REPO_URL}" || { echo "错误: git clone 失败"; exit 1; }
                            fi

                            echo "✅ 代码同步完成"
                        '
                    """
                }
            }
        }

        stage('Execute Operation') {
            steps {
                script {
                    echo "=== 执行操作：${params.OperationType} ==="

                    if (params.OperationType == 'OnlyPlatformUpdate') {
                        echo "执行平台更新操作..."
                        sh """
                            ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                                set -euo pipefail  # 启用严格模式

                                # 设置参数变量
                                BASE_IMAGE="${params.BaseImage}"
                                SRC_IMAGE="${params.SrcImage}"
                                DEST_IMAGE="${params.DestImageName}"
                                BUSINESS_NAME="${params.BusinessName}"
                                BUSINESS="${params.Business}"
                                WORK_DIR="${WORK_DIR}"

                                # 验证关键变量
                                if [ -z "\${WORK_DIR}" ] || [ -z "\${BASE_IMAGE}" ] || [ -z "\${SRC_IMAGE}" ] || [ -z "\${DEST_IMAGE}" ] || [ -z "\${BUSINESS_NAME}" ] || [ -z "\${BUSINESS}" ]; then
                                    echo "错误: 关键变量未设置"
                                    echo "WORK_DIR: \${WORK_DIR}"
                                    echo "BaseImage: \${BASE_IMAGE}"
                                    echo "SrcImage: \${SRC_IMAGE}"
                                    echo "DestImageName: \${DEST_IMAGE}"
                                    echo "BusinessName: \${BUSINESS_NAME}"
                                    echo "Business: \${BUSINESS}"
                                    exit 1
                                fi

                                # 安全地切换到脚本目录
                                SCRIPT_DIR="\${WORK_DIR}/bpmax-globaldata/saas-plugins-base-env"
                                if [ ! -d "\${SCRIPT_DIR}" ]; then
                                    echo "错误: 脚本目录不存在: \${SCRIPT_DIR}"
                                    exit 1
                                fi

                                cd "\${SCRIPT_DIR}" || { echo "错误: 无法切换到脚本目录"; exit 1; }

                                # 检查脚本是否存在且可执行
                                if [ ! -x "./docker_image_merge.sh" ]; then
                                    echo "错误: docker_image_merge.sh 脚本不存在或不可执行"
                                    exit 1
                                fi

                                echo "执行镜像合并..."
                                echo "BaseImage: \${BASE_IMAGE}"
                                echo "SrcImage: \${SRC_IMAGE}"
                                echo "DestImageName: \${DEST_IMAGE}"

                                ./docker_image_merge.sh -a "\${BASE_IMAGE}" -b "\${SRC_IMAGE}" -o "\${DEST_IMAGE}" --cleanup || {
                                    echo "错误: 镜像合并失败"
                                    exit 1
                                }

                                echo "✅ 镜像合并完成，准备推送和注册"
                            '
                        """
                    } else if (params.OperationType == 'BusinessPluginsCreateAndUpdate') {
                        echo "执行业务插件创建和更新操作..."
                        sh """
                            ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                                set -euo pipefail  # 启用严格模式

                                # 设置参数变量
                                BASE_IMAGE="${params.BaseImage}"
                                SRC_IMAGE="${params.SrcImage}"
                                DEST_IMAGE="${params.DestImageName}"
                                BUSINESS="${params.Business}"
                                BUSINESS_NAME="${params.BusinessName}"
                                WORK_DIR="${WORK_DIR}"

                                # 验证关键变量
                                if [ -z "\${WORK_DIR}" ] || [ -z "\${BASE_IMAGE}" ] || [ -z "\${SRC_IMAGE}" ] || [ -z "\${DEST_IMAGE}" ] || [ -z "\${BUSINESS}" ] || [ -z "\${BUSINESS_NAME}" ]; then
                                    echo "错误: 关键变量未设置"
                                    echo "WORK_DIR: \${WORK_DIR}"
                                    echo "BaseImage: \${BASE_IMAGE}"
                                    echo "SrcImage: \${SRC_IMAGE}"
                                    echo "DestImageName: \${DEST_IMAGE}"
                                    echo "Business: \${BUSINESS}"
                                    echo "BusinessName: \${BUSINESS_NAME}"
                                    exit 1
                                fi

                                # 安全地切换到脚本目录
                                SCRIPT_DIR="\${WORK_DIR}/bpmax-globaldata/saas-plugins-base-env"
                                if [ ! -d "\${SCRIPT_DIR}" ]; then
                                    echo "错误: 脚本目录不存在: \${SCRIPT_DIR}"
                                    exit 1
                                fi

                                cd "\${SCRIPT_DIR}" || { echo "错误: 无法切换到脚本目录"; exit 1; }

                                # 检查脚本是否存在且可执行
                                if [ ! -x "./docker_image_merge.sh" ]; then
                                    echo "错误: docker_image_merge.sh 脚本不存在或不可执行"
                                    exit 1
                                fi

                                # 检查环境文件是否存在
                                ENV_FILE="\${SCRIPT_DIR}/\${BUSINESS}.env"
                                if [ ! -f "\${ENV_FILE}" ]; then
                                    echo "错误: 环境文件不存在: \${ENV_FILE}"
                                    exit 1
                                fi

                                echo "执行镜像合并生成预览版本..."
                                echo "BaseImage: \${BASE_IMAGE}"
                                echo "SrcImage: \${SRC_IMAGE}"
                                echo "DestImageName: \${DEST_IMAGE}-pre"

                                ./docker_image_merge.sh -a "\${BASE_IMAGE}" -b "\${SRC_IMAGE}" -o "\${DEST_IMAGE}-pre" --cleanup || {
                                    echo "错误: 镜像合并失败"
                                    exit 1
                                }

                                echo "停止并删除现有容器..."
                                docker rm -f "\${BUSINESS}" 2>/dev/null || echo "容器 \${BUSINESS} 不存在或已停止"

                                echo "启动新容器..."
                                docker run --name "\${BUSINESS}" -it -d -p 12345:8080 --env-file="\${ENV_FILE}" "\${DEST_IMAGE}-pre" || {
                                    echo "错误: 容器启动失败"
                                    exit 1
                                }

                                echo "等待容器启动..."
                                sleep 10

                                echo "检查容器状态..."
                                if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "\${BUSINESS}"; then
                                    echo "✅ 容器 \${BUSINESS} 启动成功"
                                    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "\${BUSINESS}"
                                else
                                    echo "❌ 容器 \${BUSINESS} 启动失败"
                                    docker logs "\${BUSINESS}" 2>/dev/null || echo "无法获取容器日志"
                                    exit 1
                                fi

                                echo "✅ 容器启动阶段完成"
                            '
                        """
                    }
                }
            }
        }

        stage('User Confirmation') {
            when {
                expression { params.OperationType == 'BusinessPluginsCreateAndUpdate' }
            }
            steps {
                script {
                    echo "=== 等待用户确认 ==="
                    echo "容器已启动，请访问以下地址进行测试验证："
                    echo "🌐 访问地址: http://devops.bao.com"
                    echo "📋 业务类型: ${params.Business}"
                    echo "🏷️  镜像版本: ${params.DestImageName}-pre"

                    // 等待用户确认，设置2小时超时
                    def userAction
                    try {
                        timeout(time: 2, unit: 'HOURS') {
                            userAction = input(
                                message: """
🚀 容器已成功启动！

📍 访问地址: http://devops.bao.com
📋 业务类型: ${params.Business}
🏷️  镜像版本: ${params.DestImageName}-pre

请进行功能测试和验证，完成后选择下一步操作：
                                """,
                                ok: '选择操作',
                                parameters: [
                                    choice(
                                        name: 'ACTION',
                                        choices: ['完成插件更新，继续后续流程', '测试发现问题，停止流程'],
                                        description: '请选择下一步操作'
                                    ),
                                    text(
                                        name: 'FEEDBACK',
                                        defaultValue: '',
                                        description: '测试反馈或备注（可选）'
                                    )
                                ]
                            )
                        }

                        // ✅ 正常的用户选择处理逻辑移到 try 块内部
                        echo "用户选择: ${userAction.ACTION}"
                        echo "用户反馈: ${userAction.FEEDBACK}"

                        // 根据用户选择执行不同逻辑
                        if (userAction.ACTION == '测试发现问题，停止流程') {
                            echo "❌ 用户选择停止流程"
                            echo "反馈信息: ${userAction.FEEDBACK}"

                            // 停止并清理容器和中间态镜像
                            sh """
                                ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                                    set -euo pipefail

                                    # 设置变量
                                    BUSINESS="${params.Business}"
                                    DEST_IMAGE="${params.DestImageName}"

                                    echo "=== 开始清理测试资源 ==="

                                    # 停止并删除容器
                                    echo "停止并删除容器: \${BUSINESS}"
                                    docker rm -f "\${BUSINESS}" 2>/dev/null || echo "容器 \${BUSINESS} 不存在或已删除"

                                    # 删除预览镜像
                                    echo "删除预览镜像: \${DEST_IMAGE}-pre"
                                    docker rmi "\${DEST_IMAGE}-pre" 2>/dev/null || echo "镜像 \${DEST_IMAGE}-pre 不存在或已删除"

                                    echo "✅ 测试资源清理完成"
                                '
                            """

                            error("用户确认测试失败，流程已停止。反馈: ${userAction.FEEDBACK}")

                        } else {
                            echo "✅ 用户确认测试通过，继续后续流程"
                            echo "反馈信息: ${userAction.FEEDBACK}"

                            // 设置标志，表示需要先提交容器为镜像
                            env.COMMIT_CONTAINER = 'true'
                        }

                    } catch (Exception e) {
                        echo "⚠️ 用户中止操作或超时，开始清理资源..."
                        echo "异常信息: ${e.getMessage()}"

                        // 执行清理操作
                        sh """
                            ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                                set -euo pipefail

                                # 设置变量
                                BUSINESS="${params.Business}"
                                DEST_IMAGE="${params.DestImageName}"

                                echo "=== 开始清理资源 ==="

                                # 停止并删除容器
                                echo "停止并删除容器: \${BUSINESS}"
                                docker rm -f "\${BUSINESS}" 2>/dev/null || echo "容器 \${BUSINESS} 不存在或已删除"

                                # 删除预览镜像
                                echo "删除预览镜像: \${DEST_IMAGE}-pre"
                                docker rmi "\${DEST_IMAGE}-pre" 2>/dev/null || echo "镜像 \${DEST_IMAGE}-pre 不存在或已删除"

                                echo "✅ 资源清理完成"
                            '
                        """

                        // 抛出异常，终止流水线
                        error("用户中止操作，已清理相关资源")
                    }
                }
            }
        }

        stage('Commit Container') {
            when {
                expression { env.COMMIT_CONTAINER == 'true' }
            }
            steps {
                script {
                    echo "=== 提交容器为镜像 ==="
                    sh """
                        ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                            set -euo pipefail

                            # 设置变量
                            BUSINESS="${params.Business}"
                            DEST_IMAGE="${params.DestImageName}"

                            echo "容器名称: \${BUSINESS}"
                            echo "目标镜像: \${DEST_IMAGE}"

                            # 提交容器为镜像
                            docker commit --author="bao <<EMAIL>>" "\${BUSINESS}" "\${DEST_IMAGE}" || {
                                echo "错误: 容器提交失败"
                                exit 1
                            }
                            echo "✅ 容器提交成功"
                        '
                    """
                }
            }
        }

        stage('Push and Register Image') {
            when {
                anyOf {
                    expression { params.OperationType == 'OnlyPlatformUpdate' }
                    expression { env.COMMIT_CONTAINER == 'true' }
                }
            }
            steps {
                // withCredentials([usernamePassword(credentialsId: "aliyunmain", usernameVariable: "username", passwordVariable: "password")]){
                //     sh "docker login -u $username -p $password registry.cn-shanghai.aliyuncs.com"
                // }
                script {
                    echo "=== 推送镜像和注册服务 ==="
                    sh """
                        ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                            set -euo pipefail

                            # 设置变量
                            DEST_IMAGE="${params.DestImageName}"
                            BUSINESS="${params.Business}"
                            BUSINESS_NAME="${params.BusinessName}"
                            REGISTRY_ENV="${params.RegistryEnv}"

                            # 验证BusinessName必填
                            if [ -z "\${BUSINESS_NAME}" ]; then
                                echo "错误: BUSINESS_NAME 参数未设置"
                                exit 1
                            fi

                            echo "=== 开始推送镜像 ==="
                            echo "镜像地址: \${DEST_IMAGE}"

                            # 推送镜像到镜像库
                            # registry.cn-shanghai.aliyuncs.com/bpmax-saas/saas-xunjian
                            docker push "\${DEST_IMAGE}" || {
                                echo "错误: 镜像推送失败"
                                exit 1
                            }
                            echo "✅ 镜像推送成功"

                            echo "=== 开始注册镜像到服务 ==="

                            # 根据业务类型构建不同的JSON数据 ['xunjian', 'xunjian-peixun', 'xunjian-ai', 'xunjian-ai-peixun']
                            case "\${BUSINESS}" in
                                "xunjian")
                                    echo "构建巡检业务的JSON配置..."
                                    cat > /tmp/image_register.json << EOF
[
    {
        "image_name": "\${BUSINESS_NAME}",
        "image_url": "\${DEST_IMAGE}",
        "ports": [
            "8080",
            "443"
        ],
        "labels": [
            "standard",
            "time=\$(date -Iseconds)",
            "bpmax-xunjian",
            "SYS_ENVS_TPL=ALIACCESSKEYID:LTAI4GDNf4ut1x4r9ND8dcNc|||ALIACCESSKEYSECRET:******************************|||B_KAFKA_BROKERS:_{KAFKA_BROKERS}_|||DOMAIN:_{DOMAIN}_|||ENV_TAG:_{DOMAIN_NO_DOT}_|||ENV_MYSQL_VERSION:8.0|||ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||KAFKA_BROKERS:_{KAFKA_BROKERS}_|||MYSQL_DATABASE:_{SERVICE_ID}_|||MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PASSWORD:_{MYSQL_PASSWORD}_|||MYSQL_PORT:_{MYSQL_PORT}_|||MYSQL_TYPE:mysql|||MYSQL_USER:_{MYSQL_USER}_|||OPEN_API_SECRET_KEY:_{DOMAIN_NO_DOT}_^asdasd%wpocm@noirfeoop421rvssdmciuoi23|||OPEN_APP_ID:_{DOMAIN_NO_DOT}_5v4ggfv3_=9cr4gerg3|||OPEN_APP_SECRET:_{DOMAIN_NO_DOT}_23uf98_-e9r0udf8kjf|||OPEN_CORPORATION_ID:_{DOMAIN_NO_DOT}_|||OSSBUCKET:bpmax-saas|||OSSHOST:bpmax-saas.oss-cn-shanghai-internal.aliyuncs.com|||OSSREEGION:oss-cn-shanghai|||OSSUPLOADBUCKET:bpmax-saas|||OSSENDPOINT:oss-cn-shanghai.aliyuncs.com|||OSSFILEPATH:/_{BUSINESS_ID}_|||PLUGIN_OSS_BASE:/_{DOMAIN_NO_DOT}_|||PUBLICPATH_MOBILE:|||PUBLICPATH_PC:|||REDIS_HOST:_{REDIS_HOST}_|||REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||REDIS_PASSWORD:|||REDIS_PORT:_{REDIS_PORT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^dhjuye4f8wrmnc^8sdjk|||SERVER_TYPE:automation,api|||SESSION_REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||SIGNNAME:零蝉智能|||SMSCODE:SMS_213305158|||SYNC_GROUP_USER_TOPIC_PREFIX:_{DOMAIN_NO_DOT}_|||containerName:_{DOMAIN_NO_DOT}_",
            "SYS_PREHOOK=entrypoint-pre-xunjian.sh",
            "SYS_POSTHOOK=entrypoint-post-xunjian.sh",
            "SYS_VOL=nginx:/var/log/nginx/|||huanpingtong-server:/var/www/huanpingtong-server/logs/|||bpmax-server:/var/www/bpmax-server/logs/",
            "SYS_ENTRANCE_LOG=nginx/access.log"
        ]
    }
]
EOF
                                    ;;
                                "xunjian-peixun")
                                    echo "构建培训业务的JSON配置..."
                                    cat > /tmp/image_register.json << EOF
[
    {
        "image_name": "\${BUSINESS_NAME}",
        "image_url": "\${DEST_IMAGE}",
        "ports": [
            "8080",
            "443"
        ],
        "labels": [
            "standard",
            "time=\$(date -Iseconds)",
            "bpmax-peixun",
            "SYS_ENVS_TPL=ALIACCESSKEYID:LTAI4GDNf4ut1x4r9ND8dcNc|||ALIACCESSKEYSECRET:******************************|||B_KAFKA_BROKERS:_{KAFKA_BROKERS}_|||DOMAIN:_{DOMAIN}_|||ENV_TAG:_{DOMAIN_NO_DOT}_|||ENV_MYSQL_VERSION:8.0|||ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||KAFKA_BROKERS:_{KAFKA_BROKERS}_|||MYSQL_DATABASE:_{SERVICE_ID}_|||MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PASSWORD:_{MYSQL_PASSWORD}_|||MYSQL_PORT:_{MYSQL_PORT}_|||MYSQL_TYPE:mysql|||MYSQL_USER:_{MYSQL_USER}_|||OPEN_API_SECRET_KEY:_{DOMAIN_NO_DOT}_^asdasd%wpocm@noirfeoop421rvssdmciuoi23|||OPEN_APP_ID:_{DOMAIN_NO_DOT}_5v4ggfv3_=9cr4gerg3|||OPEN_APP_SECRET:_{DOMAIN_NO_DOT}_23uf98_-e9r0udf8kjf|||OPEN_CORPORATION_ID:_{DOMAIN_NO_DOT}_|||OSSBUCKET:bpmax-saas|||OSSHOST:bpmax-saas.oss-cn-shanghai-internal.aliyuncs.com|||OSSREEGION:oss-cn-shanghai|||OSSUPLOADBUCKET:bpmax-saas|||OSSENDPOINT:oss-cn-shanghai.aliyuncs.com|||OSSFILEPATH:/_{BUSINESS_ID}_|||PLUGIN_OSS_BASE:/_{DOMAIN_NO_DOT}_|||PUBLICPATH_MOBILE:|||PUBLICPATH_PC:|||REDIS_HOST:_{REDIS_HOST}_|||REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||REDIS_PASSWORD:|||REDIS_PORT:_{REDIS_PORT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^dhjuye4f8wrmnc^8sdjk|||SERVER_TYPE:automation,api|||SESSION_REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||SIGNNAME:零蝉智能|||SMSCODE:SMS_213305158|||SYNC_GROUP_USER_TOPIC_PREFIX:_{DOMAIN_NO_DOT}_|||containerName:_{DOMAIN_NO_DOT}_",
            "SYS_PREHOOK=entrypoint-pre-xunjian-peixun.sh",
            "SYS_POSTHOOK=entrypoint-post-xunjian-peixun.sh",
            "SYS_VOL=nginx:/var/log/nginx/|||huanpingtong-server:/var/www/huanpingtong-server/logs/|||bpmax-server:/var/www/bpmax-server/logs/",
            "SYS_ENTRANCE_LOG=nginx/access.log"
        ]
    }
]
EOF
                                    ;;

                                "xunjian-ai")
                                    echo "构建培训业务的JSON配置..."
                                    cat > /tmp/image_register.json << EOF
[
    {
        "image_name": "\${BUSINESS_NAME}",
        "image_url": "\${DEST_IMAGE}",
        "ports": [
            "8080",
            "443"
        ],
        "labels": [
            "standard",
            "time=\$(date -Iseconds)",
            "bpmax-peixun",
            "SYS_ENVS_TPL=ALIACCESSKEYID:LTAI4GDNf4ut1x4r9ND8dcNc|||ALIACCESSKEYSECRET:******************************|||B_KAFKA_BROKERS:_{KAFKA_BROKERS}_|||DOMAIN:_{DOMAIN}_|||ENV_TAG:_{DOMAIN_NO_DOT}_|||ENV_MYSQL_VERSION:8.0|||ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||KAFKA_BROKERS:_{KAFKA_BROKERS}_|||MYSQL_DATABASE:_{SERVICE_ID}_|||MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PASSWORD:_{MYSQL_PASSWORD}_|||MYSQL_PORT:_{MYSQL_PORT}_|||MYSQL_TYPE:mysql|||MYSQL_USER:_{MYSQL_USER}_|||OPEN_API_SECRET_KEY:_{DOMAIN_NO_DOT}_^asdasd%wpocm@noirfeoop421rvssdmciuoi23|||OPEN_APP_ID:_{DOMAIN_NO_DOT}_5v4ggfv3_=9cr4gerg3|||OPEN_APP_SECRET:_{DOMAIN_NO_DOT}_23uf98_-e9r0udf8kjf|||OPEN_CORPORATION_ID:_{DOMAIN_NO_DOT}_|||OSSBUCKET:bpmax-saas|||OSSHOST:bpmax-saas.oss-cn-shanghai-internal.aliyuncs.com|||OSSREEGION:oss-cn-shanghai|||OSSUPLOADBUCKET:bpmax-saas|||OSSENDPOINT:oss-cn-shanghai.aliyuncs.com|||OSSFILEPATH:/_{BUSINESS_ID}_|||PLUGIN_OSS_BASE:/_{DOMAIN_NO_DOT}_|||PUBLICPATH_MOBILE:|||PUBLICPATH_PC:|||REDIS_HOST:_{REDIS_HOST}_|||REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||REDIS_PASSWORD:|||REDIS_PORT:_{REDIS_PORT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^dhjuye4f8wrmnc^8sdjk|||SERVER_TYPE:automation,api|||SESSION_REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||SIGNNAME:零蝉智能|||SMSCODE:SMS_213305158|||SYNC_GROUP_USER_TOPIC_PREFIX:_{DOMAIN_NO_DOT}_|||containerName:_{DOMAIN_NO_DOT}_",
            "SYS_PREHOOK=entrypoint-pre-xunjian-ai.sh",
            "SYS_POSTHOOK=entrypoint-post-xunjian-ai.sh",
            "SYS_VOL=nginx:/var/log/nginx/|||huanpingtong-server:/var/www/huanpingtong-server/logs/|||bpmax-server:/var/www/bpmax-server/logs/",
            "SYS_ENTRANCE_LOG=nginx/access.log"
        ]
    }
]
EOF

                                    ;;

                                "xunjian-ai-peixun")
                                    echo "构建培训业务的JSON配置..."
                                    cat > /tmp/image_register.json << EOF
[
    {
        "image_name": "\${BUSINESS_NAME}",
        "image_url": "\${DEST_IMAGE}",
        "ports": [
            "8080",
            "443"
        ],
        "labels": [
            "standard",
            "time=\$(date -Iseconds)",
            "bpmax-peixun",
            "SYS_ENVS_TPL=ALIACCESSKEYID:LTAI4GDNf4ut1x4r9ND8dcNc|||ALIACCESSKEYSECRET:******************************|||B_KAFKA_BROKERS:_{KAFKA_BROKERS}_|||DOMAIN:_{DOMAIN}_|||ENV_TAG:_{DOMAIN_NO_DOT}_|||ENV_MYSQL_VERSION:8.0|||ES_NAMESPACE:_{DOMAIN_NO_DOT}_|||ES_SERVER:http://_{ES_SERVER}_|||KAFKA_BROKERS:_{KAFKA_BROKERS}_|||MYSQL_DATABASE:_{SERVICE_ID}_|||MYSQL_HOST:_{MYSQL_HOST}_|||MYSQL_PASSWORD:_{MYSQL_PASSWORD}_|||MYSQL_PORT:_{MYSQL_PORT}_|||MYSQL_TYPE:mysql|||MYSQL_USER:_{MYSQL_USER}_|||OPEN_API_SECRET_KEY:_{DOMAIN_NO_DOT}_^asdasd%wpocm@noirfeoop421rvssdmciuoi23|||OPEN_APP_ID:_{DOMAIN_NO_DOT}_5v4ggfv3_=9cr4gerg3|||OPEN_APP_SECRET:_{DOMAIN_NO_DOT}_23uf98_-e9r0udf8kjf|||OPEN_CORPORATION_ID:_{DOMAIN_NO_DOT}_|||OSSBUCKET:bpmax-saas|||OSSHOST:bpmax-saas.oss-cn-shanghai-internal.aliyuncs.com|||OSSREEGION:oss-cn-shanghai|||OSSUPLOADBUCKET:bpmax-saas|||OSSENDPOINT:oss-cn-shanghai.aliyuncs.com|||OSSFILEPATH:/_{BUSINESS_ID}_|||PLUGIN_OSS_BASE:/_{DOMAIN_NO_DOT}_|||PUBLICPATH_MOBILE:|||PUBLICPATH_PC:|||REDIS_HOST:_{REDIS_HOST}_|||REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||REDIS_PASSWORD:|||REDIS_PORT:_{REDIS_PORT}_|||RUNTIME_ENV:_{DOMAIN_NO_DOT}_|||SECRET_KEY:_{DOMAIN_NO_DOT}_^dhjuye4f8wrmnc^8sdjk|||SERVER_TYPE:automation,api|||SESSION_REDIS_KEY_PREFIX:_{DOMAIN_NO_DOT}_:|||SIGNNAME:零蝉智能|||SMSCODE:SMS_213305158|||SYNC_GROUP_USER_TOPIC_PREFIX:_{DOMAIN_NO_DOT}_|||containerName:_{DOMAIN_NO_DOT}_",
            "SYS_PREHOOK=entrypoint-pre-xunjian-ai-peixun.sh",
            "SYS_POSTHOOK=entrypoint-post-xunjian-ai-peixun.sh",
            "SYS_VOL=nginx:/var/log/nginx/|||huanpingtong-server:/var/www/huanpingtong-server/logs/|||bpmax-server:/var/www/bpmax-server/logs/",
            "SYS_ENTRANCE_LOG=nginx/access.log"
        ]
    }
]
EOF

                                    ;;
                                *)
                                    echo "错误: 不支持的业务类型: \${BUSINESS}"
                                    exit 1
                                    ;;
                            esac

                            echo "生成的注册JSON:"
                            cat /tmp/image_register.json

                            # 根据 RegistryEnv 参数确定注册服务地址
                            if [ "\${REGISTRY_ENV}" = "pre" ]; then
                                REGISTRY_URL="192.168.31.104:18888"
                            elif [ "\${REGISTRY_ENV}" = "prod" ]; then
                                REGISTRY_URL="101.133.220.214:18888"
                            else
                                echo "错误: 不支持的 RegistryEnv 值: \${REGISTRY_ENV}"
                                exit 1
                            fi

                            echo "使用注册服务地址: \${REGISTRY_URL} (环境: \${REGISTRY_ENV})"

                            # 执行注册请求
                            curl --location "\${REGISTRY_URL}/api/v1/service/image/create" \
                                 --header "Content-Type: application/json" \
                                 --header "Authorization: Basic ZGV2b3BzOjMtNTQ2LV9pdWhoNTQ5OA==" \
                                 --data "@/tmp/image_register.json" || {
                                echo "错误: 镜像注册失败"
                                exit 1
                            }

                            # 清理临时文件
                            rm -f /tmp/image_register.json

                            echo "✅ 镜像注册成功"
                            echo "=== 推送和注册完成 ==="
                        '
                    """
                }
            }
        }

        stage('Post Confirmation Actions') {
            when {
                allOf {
                    expression { params.OperationType == 'BusinessPluginsCreateAndUpdate' }
                    expression { env.COMMIT_CONTAINER == 'true' }
                }
            }
            steps {
                script {
                    echo "=== 执行确认后的清理操作 ==="

                    sh """
                        ssh -o StrictHostKeyChecking=no -i "${SSH_KEY}" root@${REMOTE_HOST} '
                            # 设置变量
                            BUSINESS="${params.Business}"
                            DEST_IMAGE="${params.DestImageName}"

                            echo "执行后续清理操作..."

                            # 显示当前运行的容器状态
                            echo "当前运行的容器:"
                            docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "\${BUSINESS}" || echo "容器未运行"

                            # 清理测试容器（因为已经提交为正式镜像）
                            echo "清理测试容器..."
                            docker rm -f "\${BUSINESS}" 2>/dev/null || echo "容器已清理或不存在"

                            # 清理中间态预览镜像（因为已经生成正式镜像）
                            echo "清理预览镜像..."
                            docker rmi "\${DEST_IMAGE}-pre" 2>/dev/null || echo "预览镜像已清理或不存在"

                            echo "✅ 清理操作完成"
                        '
                    """

                    echo "🎉 BusinessPluginsCreateAndUpdate 流程全部完成！"
                }
            }
        }
    }

    post {
        always {
            script {
                echo "=== 构建完成 ==="
                echo "操作类型: ${params.OperationType}"
                echo "目标镜像: ${params.DestImageName}"
                if (params.OperationType == 'BusinessPluginsCreateAndUpdate') {
                    echo "业务名称: ${params.Business}"
                    echo "容器访问地址: http://devops.bao.com"
                }
            }
        }
        success {
            echo "✅ 构建成功完成"
        }
        failure {
            echo "❌ 构建失败"
        }
    }
}


